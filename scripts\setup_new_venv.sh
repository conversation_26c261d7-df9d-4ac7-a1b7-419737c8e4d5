#!/bin/bash

# New Virtual Environment Setup Script
# This script sets up a new Python virtual environment for AICleaner development
# MCP servers are already installed globally and don't need reinstallation

set -e

echo "🐍 Setting up new Python Virtual Environment for AICleaner"
echo "=========================================================="

# Get the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR"

# 1. Create new virtual environment
VENV_NAME="${1:-venv}"
echo "📦 Creating virtual environment: $VENV_NAME"

if [ -d "$VENV_NAME" ]; then
    echo "⚠️  Virtual environment '$VENV_NAME' already exists"
    read -p "Do you want to remove it and create a new one? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf "$VENV_NAME"
        echo "🗑️  Removed existing virtual environment"
    else
        echo "❌ Aborted - keeping existing virtual environment"
        exit 1
    fi
fi

python3 -m venv "$VENV_NAME"
echo "✅ Virtual environment '$VENV_NAME' created"

# 2. Activate virtual environment
source "$VENV_NAME/bin/activate"
echo "✅ Virtual environment activated"

# 3. Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# 4. Install Python dependencies
echo "📚 Installing Python dependencies..."
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
    echo "✅ Installed dependencies from requirements.txt"
else
    echo "⚠️  No requirements.txt found - installing basic testing dependencies"
    pip install pytest pytest-mock requests-mock
fi

# 5. Verify MCP servers are still available (they should be global)
echo "🔍 Verifying MCP servers are available..."
if command -v notion-mcp-server &> /dev/null; then
    echo "✅ MCP servers are available globally"
    echo "   Node.js: $(node --version)"
    echo "   Notion MCP: $(which notion-mcp-server)"
    echo "   Filesystem MCP: $(which mcp-server-filesystem)"
else
    echo "❌ MCP servers not found - run setup_mcp_environment.sh first"
    exit 1
fi

# 6. Load environment configuration
echo "🔐 Loading environment configuration..."
if [ -f ".env.secrets" ]; then
    source .env.secrets
    echo "✅ Environment secrets loaded"
else
    echo "⚠️  .env.secrets not found - copy from .env.secrets.template"
fi

if [ -f ".env.mcp" ]; then
    source .env.mcp
    echo "✅ MCP configuration loaded"
fi

# 7. Run a quick test to verify everything works
echo "🧪 Running quick verification test..."
if python -c "import pytest; print('✅ pytest available')" 2>/dev/null; then
    echo "✅ Python testing environment ready"
else
    echo "❌ Python testing environment has issues"
    exit 1
fi

echo ""
echo "🎉 New Virtual Environment Setup Complete!"
echo "=========================================="
echo ""
echo "📋 Summary:"
echo "  ✅ Python virtual environment '$VENV_NAME' created and activated"
echo "  ✅ Python dependencies installed"
echo "  ✅ MCP servers available globally (no reinstallation needed)"
echo "  ✅ Environment configuration loaded"
echo ""
echo "🚀 Ready for development! To activate this environment in the future:"
echo "   source $VENV_NAME/bin/activate"
echo ""
echo "💡 Remember: MCP servers are installed globally and persist across venvs!"
