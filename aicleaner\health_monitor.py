"""
Health Monitoring System for AICleaner
Provides comprehensive system health monitoring, metrics collection, and diagnostic capabilities
"""

import os
import json
import time
import psutil
import logging
import threading
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum


class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class ComponentType(Enum):
    """System component types"""
    AI_ANALYSIS = "ai_analysis"
    HOME_ASSISTANT = "home_assistant"
    CONFIGURATION = "configuration"
    STORAGE = "storage"
    NETWORK = "network"
    MEMORY = "memory"
    CACHE = "cache"
    NOTIFICATIONS = "notifications"


@dataclass
class HealthCheck:
    """Individual health check result"""
    component: str
    status: HealthStatus
    message: str
    timestamp: str
    details: Dict[str, Any] = None
    metrics: Dict[str, float] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}
        if self.metrics is None:
            self.metrics = {}


@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_usage_percent: float
    disk_free_mb: float
    uptime_seconds: float
    active_threads: int
    
    # AICleaner specific metrics
    ai_analysis_count: int = 0
    cache_hit_rate: float = 0.0
    average_analysis_time: float = 0.0
    active_zones: int = 0
    total_tasks: int = 0
    error_count: int = 0


class HealthMonitor:
    """
    Comprehensive health monitoring system for AICleaner
    
    Features:
    - Real-time system health monitoring
    - Performance metrics collection
    - Component health checks
    - Diagnostic utilities
    - Health history tracking
    - Alert generation
    """
    
    def __init__(self, data_dir: str = "/data"):
        """
        Initialize health monitoring system
        
        Args:
            data_dir: Directory for storing health data
        """
        self.data_dir = data_dir
        self.health_dir = os.path.join(data_dir, "health")
        self.logger = logging.getLogger(__name__)
        
        # Ensure health directory exists
        os.makedirs(self.health_dir, exist_ok=True)
        
        # Health check registry
        self.health_checks = {}
        self.metrics_history = []
        self.max_history_size = 1000
        
        # System start time for uptime calculation
        self.start_time = time.time()
        
        # Monitoring thread
        self.monitoring_thread = None
        self.monitoring_active = False
        self.monitoring_interval = 60  # seconds
        
        # Initialize default health checks
        self._register_default_health_checks()
        
        self.logger.info("Health monitoring system initialized")
    
    def _register_default_health_checks(self):
        """Register default health checks"""
        self.register_health_check("system_resources", self._check_system_resources)
        self.register_health_check("disk_space", self._check_disk_space)
        self.register_health_check("memory_usage", self._check_memory_usage)
        self.register_health_check("configuration", self._check_configuration)
        self.register_health_check("data_integrity", self._check_data_integrity)
    
    def register_health_check(self, name: str, check_function):
        """
        Register a health check function
        
        Args:
            name: Unique name for the health check
            check_function: Function that returns HealthCheck object
        """
        self.health_checks[name] = check_function
        self.logger.debug(f"Registered health check: {name}")
    
    def get_system_health(self) -> Dict[str, Any]:
        """
        Get comprehensive system health status
        
        Returns:
            Dictionary containing overall health status and component details
        """
        health_results = {}
        overall_status = HealthStatus.HEALTHY
        
        # Run all registered health checks
        for check_name, check_function in self.health_checks.items():
            try:
                result = check_function()
                health_results[check_name] = asdict(result)
                
                # Update overall status based on worst component status
                if result.status == HealthStatus.CRITICAL:
                    overall_status = HealthStatus.CRITICAL
                elif result.status == HealthStatus.WARNING and overall_status != HealthStatus.CRITICAL:
                    overall_status = HealthStatus.WARNING
                    
            except Exception as e:
                self.logger.error(f"Health check {check_name} failed: {e}")
                health_results[check_name] = asdict(HealthCheck(
                    component=check_name,
                    status=HealthStatus.UNKNOWN,
                    message=f"Health check failed: {str(e)}",
                    timestamp=datetime.now(timezone.utc).isoformat()
                ))
                if overall_status == HealthStatus.HEALTHY:
                    overall_status = HealthStatus.WARNING
        
        return {
            "overall_status": overall_status.value,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "components": health_results,
            "summary": self._generate_health_summary(health_results)
        }
    
    def get_system_metrics(self) -> SystemMetrics:
        """
        Collect current system performance metrics
        
        Returns:
            SystemMetrics object with current system state
        """
        try:
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage(self.data_dir)
            
            # Calculate uptime
            uptime_seconds = time.time() - self.start_time
            
            # Thread count
            active_threads = threading.active_count()
            
            # AICleaner specific metrics (would be populated by the main system)
            aicleaner_metrics = self._get_aicleaner_metrics()
            
            return SystemMetrics(
                timestamp=datetime.now(timezone.utc).isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / (1024 * 1024),
                disk_usage_percent=disk.percent,
                disk_free_mb=disk.free / (1024 * 1024),
                uptime_seconds=uptime_seconds,
                active_threads=active_threads,
                **aicleaner_metrics
            )
            
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
            return SystemMetrics(
                timestamp=datetime.now(timezone.utc).isoformat(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                disk_usage_percent=0.0,
                disk_free_mb=0.0,
                uptime_seconds=0.0,
                active_threads=0
            )
    
    def _get_aicleaner_metrics(self) -> Dict[str, Any]:
        """Get AICleaner specific metrics"""
        try:
            # Read metrics from state files if available
            state_file = os.path.join(self.data_dir, "state.json")
            metrics_file = os.path.join(self.health_dir, "metrics.json")
            
            metrics = {
                "ai_analysis_count": 0,
                "cache_hit_rate": 0.0,
                "average_analysis_time": 0.0,
                "active_zones": 0,
                "total_tasks": 0,
                "error_count": 0
            }
            
            # Load from metrics file if exists
            if os.path.exists(metrics_file):
                with open(metrics_file, 'r') as f:
                    stored_metrics = json.load(f)
                    metrics.update(stored_metrics)
            
            # Count zones and tasks from state file
            if os.path.exists(state_file):
                with open(state_file, 'r') as f:
                    state = json.load(f)
                    if isinstance(state, dict):
                        zone_count = 0
                        task_count = 0
                        for key, value in state.items():
                            if key not in ['schema_version', 'migrated_at', 'last_backup']:
                                zone_count += 1
                                if isinstance(value, dict) and 'tasks' in value:
                                    task_count += len(value['tasks'])
                        
                        metrics["active_zones"] = zone_count
                        metrics["total_tasks"] = task_count
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error getting AICleaner metrics: {e}")
            return {
                "ai_analysis_count": 0,
                "cache_hit_rate": 0.0,
                "average_analysis_time": 0.0,
                "active_zones": 0,
                "total_tasks": 0,
                "error_count": 0
            }
    
    def update_aicleaner_metrics(self, **kwargs):
        """
        Update AICleaner specific metrics
        
        Args:
            **kwargs: Metric name-value pairs to update
        """
        try:
            metrics_file = os.path.join(self.health_dir, "metrics.json")
            
            # Load existing metrics
            metrics = {}
            if os.path.exists(metrics_file):
                with open(metrics_file, 'r') as f:
                    metrics = json.load(f)
            
            # Update with new values
            metrics.update(kwargs)
            metrics["last_updated"] = datetime.now(timezone.utc).isoformat()
            
            # Save updated metrics
            with open(metrics_file, 'w') as f:
                json.dump(metrics, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error updating AICleaner metrics: {e}")
    
    def _check_system_resources(self) -> HealthCheck:
        """Check system resource usage"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            status = HealthStatus.HEALTHY
            message = "System resources are healthy"
            
            if cpu_percent > 90 or memory.percent > 90:
                status = HealthStatus.CRITICAL
                message = f"High resource usage: CPU {cpu_percent:.1f}%, Memory {memory.percent:.1f}%"
            elif cpu_percent > 70 or memory.percent > 70:
                status = HealthStatus.WARNING
                message = f"Moderate resource usage: CPU {cpu_percent:.1f}%, Memory {memory.percent:.1f}%"
            
            return HealthCheck(
                component="system_resources",
                status=status,
                message=message,
                timestamp=datetime.now(timezone.utc).isoformat(),
                metrics={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_used_mb": memory.used / (1024 * 1024)
                }
            )
            
        except Exception as e:
            return HealthCheck(
                component="system_resources",
                status=HealthStatus.UNKNOWN,
                message=f"Failed to check system resources: {str(e)}",
                timestamp=datetime.now(timezone.utc).isoformat()
            )
    
    def _check_disk_space(self) -> HealthCheck:
        """Check available disk space"""
        try:
            disk = psutil.disk_usage(self.data_dir)
            free_gb = disk.free / (1024 * 1024 * 1024)
            
            status = HealthStatus.HEALTHY
            message = f"Disk space healthy: {free_gb:.1f}GB free"
            
            if free_gb < 0.1:  # Less than 100MB
                status = HealthStatus.CRITICAL
                message = f"Critical: Only {free_gb:.1f}GB free"
            elif free_gb < 0.5:  # Less than 500MB
                status = HealthStatus.WARNING
                message = f"Warning: Only {free_gb:.1f}GB free"
            
            return HealthCheck(
                component="disk_space",
                status=status,
                message=message,
                timestamp=datetime.now(timezone.utc).isoformat(),
                metrics={
                    "disk_usage_percent": disk.percent,
                    "free_gb": free_gb,
                    "total_gb": disk.total / (1024 * 1024 * 1024)
                }
            )
            
        except Exception as e:
            return HealthCheck(
                component="disk_space",
                status=HealthStatus.UNKNOWN,
                message=f"Failed to check disk space: {str(e)}",
                timestamp=datetime.now(timezone.utc).isoformat()
            )
    
    def _check_memory_usage(self) -> HealthCheck:
        """Check memory usage patterns"""
        try:
            memory = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info()
            
            process_mb = process_memory.rss / (1024 * 1024)
            
            status = HealthStatus.HEALTHY
            message = f"Memory usage normal: {process_mb:.1f}MB"
            
            if process_mb > 500:  # More than 500MB
                status = HealthStatus.WARNING
                message = f"High memory usage: {process_mb:.1f}MB"
            elif process_mb > 1000:  # More than 1GB
                status = HealthStatus.CRITICAL
                message = f"Critical memory usage: {process_mb:.1f}MB"
            
            return HealthCheck(
                component="memory_usage",
                status=status,
                message=message,
                timestamp=datetime.now(timezone.utc).isoformat(),
                metrics={
                    "process_memory_mb": process_mb,
                    "system_memory_percent": memory.percent,
                    "available_mb": memory.available / (1024 * 1024)
                }
            )
            
        except Exception as e:
            return HealthCheck(
                component="memory_usage",
                status=HealthStatus.UNKNOWN,
                message=f"Failed to check memory usage: {str(e)}",
                timestamp=datetime.now(timezone.utc).isoformat()
            )
    
    def _check_configuration(self) -> HealthCheck:
        """Check configuration validity"""
        try:
            config_file = "/data/options.json"
            
            if not os.path.exists(config_file):
                return HealthCheck(
                    component="configuration",
                    status=HealthStatus.WARNING,
                    message="Configuration file not found",
                    timestamp=datetime.now(timezone.utc).isoformat()
                )
            
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # Basic validation
            required_fields = ['gemini_api_key', 'zones']
            missing_fields = [field for field in required_fields if not config.get(field)]
            
            if missing_fields:
                return HealthCheck(
                    component="configuration",
                    status=HealthStatus.CRITICAL,
                    message=f"Missing required configuration: {', '.join(missing_fields)}",
                    timestamp=datetime.now(timezone.utc).isoformat()
                )
            
            return HealthCheck(
                component="configuration",
                status=HealthStatus.HEALTHY,
                message="Configuration is valid",
                timestamp=datetime.now(timezone.utc).isoformat(),
                details={
                    "zones_configured": len(config.get('zones', [])),
                    "has_api_key": bool(config.get('gemini_api_key'))
                }
            )
            
        except Exception as e:
            return HealthCheck(
                component="configuration",
                status=HealthStatus.CRITICAL,
                message=f"Configuration check failed: {str(e)}",
                timestamp=datetime.now(timezone.utc).isoformat()
            )
    
    def _check_data_integrity(self) -> HealthCheck:
        """Check data file integrity"""
        try:
            state_file = os.path.join(self.data_dir, "state.json")
            
            if not os.path.exists(state_file):
                return HealthCheck(
                    component="data_integrity",
                    status=HealthStatus.WARNING,
                    message="State file not found (new installation)",
                    timestamp=datetime.now(timezone.utc).isoformat()
                )
            
            # Try to load and validate state file
            with open(state_file, 'r') as f:
                state = json.load(f)
            
            if not isinstance(state, dict):
                return HealthCheck(
                    component="data_integrity",
                    status=HealthStatus.CRITICAL,
                    message="State file is corrupted (not a dictionary)",
                    timestamp=datetime.now(timezone.utc).isoformat()
                )
            
            # Check file size
            file_size = os.path.getsize(state_file)
            if file_size > 10 * 1024 * 1024:  # 10MB
                status = HealthStatus.WARNING
                message = f"Large state file: {file_size / (1024*1024):.1f}MB"
            else:
                status = HealthStatus.HEALTHY
                message = "Data integrity is good"
            
            return HealthCheck(
                component="data_integrity",
                status=status,
                message=message,
                timestamp=datetime.now(timezone.utc).isoformat(),
                metrics={
                    "state_file_size_mb": file_size / (1024 * 1024),
                    "zone_count": len([k for k in state.keys() if k not in ['schema_version', 'migrated_at', 'last_backup']])
                }
            )
            
        except Exception as e:
            return HealthCheck(
                component="data_integrity",
                status=HealthStatus.CRITICAL,
                message=f"Data integrity check failed: {str(e)}",
                timestamp=datetime.now(timezone.utc).isoformat()
            )
    
    def _generate_health_summary(self, health_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a summary of health check results"""
        total_checks = len(health_results)
        healthy_count = sum(1 for result in health_results.values() if result['status'] == 'healthy')
        warning_count = sum(1 for result in health_results.values() if result['status'] == 'warning')
        critical_count = sum(1 for result in health_results.values() if result['status'] == 'critical')
        unknown_count = sum(1 for result in health_results.values() if result['status'] == 'unknown')
        
        return {
            "total_checks": total_checks,
            "healthy": healthy_count,
            "warning": warning_count,
            "critical": critical_count,
            "unknown": unknown_count,
            "health_percentage": (healthy_count / total_checks * 100) if total_checks > 0 else 0
        }
    
    def save_health_report(self) -> str:
        """
        Save current health report to file
        
        Returns:
            Path to saved health report file
        """
        try:
            health_data = self.get_system_health()
            metrics_data = asdict(self.get_system_metrics())
            
            report = {
                "health": health_data,
                "metrics": metrics_data,
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = os.path.join(self.health_dir, f"health_report_{timestamp}.json")
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            self.logger.info(f"Health report saved to {report_file}")
            return report_file
            
        except Exception as e:
            self.logger.error(f"Error saving health report: {e}")
            return ""
    
    def get_health_endpoint_data(self) -> Dict[str, Any]:
        """
        Get health data formatted for HTTP endpoint response
        
        Returns:
            Dictionary suitable for JSON API response
        """
        health = self.get_system_health()
        metrics = self.get_system_metrics()
        
        return {
            "status": health["overall_status"],
            "timestamp": health["timestamp"],
            "uptime_seconds": metrics.uptime_seconds,
            "system": {
                "cpu_percent": metrics.cpu_percent,
                "memory_percent": metrics.memory_percent,
                "disk_usage_percent": metrics.disk_usage_percent,
                "active_threads": metrics.active_threads
            },
            "aicleaner": {
                "active_zones": metrics.active_zones,
                "total_tasks": metrics.total_tasks,
                "ai_analysis_count": metrics.ai_analysis_count,
                "cache_hit_rate": metrics.cache_hit_rate,
                "average_analysis_time": metrics.average_analysis_time,
                "error_count": metrics.error_count
            },
            "components": health["components"],
            "summary": health["summary"]
        }
