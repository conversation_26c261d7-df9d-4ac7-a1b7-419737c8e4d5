# AICleaner v2.0+ Home Assistant Add-on

Transform your Home Assistant into an intelligent cleaning management system with AI-powered room analysis, mobile optimization, gamification, and smart notifications.

![Supports aarch64 Architecture][aarch64-shield] ![Supports amd64 Architecture][amd64-shield] ![Supports armv7 Architecture][armv7-shield]

## About

AICleaner is a comprehensive Home Assistant add-on that uses advanced AI analysis to monitor room cleanliness across multiple zones, automatically generate cleaning tasks, and provide intelligent notifications. With Phase 3 enhancements, it now includes mobile-first design, gamification features, and smart notification systems.

## Features

### 🤖 Advanced AI Analysis
- **Multi-Zone Intelligence**: Monitor unlimited rooms with AI-powered cleanliness assessment
- **Lightning Fast**: 40%+ faster analysis with intelligent caching (12,000x speedup)
- **Multi-Model AI**: Support for Gemini, Claude, and OpenAI models
- **Scene Understanding**: Advanced computer vision for context-aware cleaning suggestions

### 📱 Mobile-First Experience
- **PWA Support**: Install as a native mobile app with offline capabilities
- **Touch-Optimized**: Responsive design with gesture controls and quick actions
- **Push Notifications**: Smart mobile alerts with personalized timing
- **Location Awareness**: Context-aware features based on your presence

### 🎮 Gamification & Motivation
- **Achievement System**: 12+ achievements with multiple rarity levels
- **Daily Challenges**: Personalized cleaning challenges with point rewards
- **Experience Points**: Level up your cleaning game with XP and streaks
- **Progress Tracking**: Visual progress indicators and motivational feedback

### 🔔 Smart Notifications
- **Intelligent Timing**: Notifications delivered at optimal times
- **Multi-Channel**: Home Assistant, mobile push, email, and webhook support
- **Personalization**: Adaptive messaging based on your preferences and behavior
- **Quiet Hours**: Respects your schedule with do-not-disturb modes

## Installation

1. Add this repository to your Home Assistant Add-on Store
2. Install the "AICleaner v2.0+" add-on
3. Configure your API keys and zones
4. Start the add-on

## Configuration

### Basic Configuration

```yaml
gemini_api_key: "your_gemini_api_key_here"
display_name: "Your Name"
zones:
  - name: "kitchen"
    camera_entity: "camera.kitchen"
    todo_list_entity: "todo.kitchen_tasks"
    notification_service: "mobile_app_your_phone"
```

### Advanced Options

The add-on supports extensive customization:

- **AI & Analysis**: Multi-model AI, predictive analytics, scene understanding
- **Mobile Features**: PWA, push notifications, gesture controls, themes
- **Gamification**: Achievements, daily challenges, experience points, streaks
- **Smart Notifications**: Intelligent timing, personalization, quiet hours

For complete configuration options, see the Configuration tab in the add-on.

## Usage

Once configured, AICleaner runs automatically:

1. **Scheduled Analysis**: Analyzes zones based on your configured frequency
2. **AI Assessment**: Uses computer vision to evaluate room cleanliness
3. **Task Generation**: Creates specific, actionable cleaning tasks
4. **Progress Tracking**: Monitors completion and updates todo lists
5. **Smart Notifications**: Sends alerts at optimal times

### Manual Control

- Use the `aicleaner.run_analysis` service for immediate analysis
- Target specific zones with the `zone_name` parameter
- Access the Lovelace card for visual monitoring and controls

## Support

- **Documentation**: Complete guides available in the add-on
- **Issues**: Report bugs via [GitHub Issues](https://github.com/sporebattyl/Aiclean/issues)
- **Community**: Join discussions on [GitHub](https://github.com/sporebattyl/Aiclean/discussions)

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history and updates.

---

[aarch64-shield]: https://img.shields.io/badge/aarch64-yes-green.svg
[amd64-shield]: https://img.shields.io/badge/amd64-yes-green.svg
[armv7-shield]: https://img.shields.io/badge/armv7-yes-green.svg
