"""
Diagnostic Tools for AICleaner
Provides comprehensive diagnostic utilities for troubleshooting, system inspection, and performance analysis
"""

import os
import json
import time
import psutil
import logging
import subprocess
import traceback
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class DiagnosticResult:
    """Result of a diagnostic check"""
    check_name: str
    status: str  # "pass", "fail", "warning", "info"
    message: str
    details: Dict[str, Any]
    timestamp: str
    duration_ms: int
    recommendations: List[str] = None

    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []


@dataclass
class SystemSnapshot:
    """Complete system state snapshot"""
    timestamp: str
    system_info: Dict[str, Any]
    process_info: Dict[str, Any]
    file_system: Dict[str, Any]
    network_info: Dict[str, Any]
    aicleaner_state: Dict[str, Any]
    configuration: Dict[str, Any]
    recent_logs: List[str]


class DiagnosticTools:
    """
    Comprehensive diagnostic and troubleshooting toolkit for AICleaner

    Features:
    - System health diagnostics
    - Configuration validation
    - Performance analysis
    - Network connectivity testing
    - File system integrity checks
    - Log analysis and pattern detection
    - Automated troubleshooting suggestions
    - System state snapshots
    """

    def __init__(self, data_dir: str = "/data", config_file: str = "/data/options.json"):
        """
        Initialize diagnostic tools

        Args:
            data_dir: AICleaner data directory
            config_file: Configuration file path
        """
        self.data_dir = data_dir
        self.config_file = config_file
        self.diagnostics_dir = os.path.join(data_dir, "diagnostics")
        self.logger = logging.getLogger(__name__)

        # Ensure diagnostics directory exists
        os.makedirs(self.diagnostics_dir, exist_ok=True)

        # Diagnostic check registry
        self.diagnostic_checks = {}
        self._register_default_checks()

        self.logger.info("Diagnostic tools initialized")

    def _register_default_checks(self):
        """Register default diagnostic checks"""
        self.diagnostic_checks = {
            "system_resources": self._check_system_resources,
            "disk_space": self._check_disk_space,
            "configuration": self._check_configuration,
            "file_permissions": self._check_file_permissions,
            "network_connectivity": self._check_network_connectivity,
            "api_endpoints": self._check_api_endpoints,
            "data_integrity": self._check_data_integrity,
            "log_analysis": self._check_log_analysis,
            "cache_health": self._check_cache_health,
            "process_health": self._check_process_health
        }

    def run_full_diagnostic(self) -> Dict[str, Any]:
        """
        Run complete diagnostic suite

        Returns:
            Comprehensive diagnostic report
        """
        start_time = time.time()
        results = {}

        self.logger.info("Starting full diagnostic suite")

        # Run all diagnostic checks
        for check_name, check_function in self.diagnostic_checks.items():
            try:
                check_start = time.time()
                result = check_function()
                check_duration = int((time.time() - check_start) * 1000)

                if isinstance(result, DiagnosticResult):
                    result.duration_ms = check_duration
                    results[check_name] = asdict(result)
                else:
                    # Handle legacy return format
                    results[check_name] = {
                        "check_name": check_name,
                        "status": "info",
                        "message": str(result),
                        "details": {},
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "duration_ms": check_duration,
                        "recommendations": []
                    }

            except Exception as e:
                self.logger.error(f"Diagnostic check {check_name} failed: {e}")
                results[check_name] = {
                    "check_name": check_name,
                    "status": "fail",
                    "message": f"Diagnostic check failed: {str(e)}",
                    "details": {"error": str(e), "traceback": traceback.format_exc()},
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "duration_ms": 0,
                    "recommendations": ["Check system logs for more details"]
                }

        # Generate overall assessment
        total_duration = int((time.time() - start_time) * 1000)
        overall_status = self._assess_overall_health(results)

        report = {
            "diagnostic_report": {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "duration_ms": total_duration,
                "overall_status": overall_status,
                "checks_run": len(results),
                "checks_passed": len([r for r in results.values() if r["status"] == "pass"]),
                "checks_failed": len([r for r in results.values() if r["status"] == "fail"]),
                "checks_warning": len([r for r in results.values() if r["status"] == "warning"])
            },
            "results": results,
            "recommendations": self._generate_recommendations(results),
            "system_snapshot": self._create_system_snapshot()
        }

        # Save diagnostic report
        self._save_diagnostic_report(report)

        self.logger.info(f"Full diagnostic completed in {total_duration}ms - Status: {overall_status}")
        return report

    def _check_system_resources(self) -> DiagnosticResult:
        """Check system resource usage"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()

            status = "pass"
            message = "System resources are healthy"
            recommendations = []

            if cpu_percent > 90:
                status = "fail"
                message = f"Critical CPU usage: {cpu_percent:.1f}%"
                recommendations.append("Reduce system load or increase CPU resources")
            elif cpu_percent > 70:
                status = "warning"
                message = f"High CPU usage: {cpu_percent:.1f}%"
                recommendations.append("Monitor CPU usage and consider optimization")

            if memory.percent > 90:
                status = "fail" if status != "fail" else status
                message += f" | Critical memory usage: {memory.percent:.1f}%"
                recommendations.append("Free up memory or increase RAM")
            elif memory.percent > 70:
                status = "warning" if status == "pass" else status
                message += f" | High memory usage: {memory.percent:.1f}%"
                recommendations.append("Monitor memory usage")

            return DiagnosticResult(
                check_name="system_resources",
                status=status,
                message=message,
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_used_gb": memory.used / (1024**3),
                    "memory_available_gb": memory.available / (1024**3)
                },
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=recommendations
            )

        except Exception as e:
            return DiagnosticResult(
                check_name="system_resources",
                status="fail",
                message=f"Failed to check system resources: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Check system monitoring tools"]
            )

    def _check_disk_space(self) -> DiagnosticResult:
        """Check disk space availability"""
        try:
            disk = psutil.disk_usage(self.data_dir)
            free_gb = disk.free / (1024**3)
            used_percent = (disk.used / disk.total) * 100

            status = "pass"
            message = f"Disk space healthy: {free_gb:.1f}GB free ({used_percent:.1f}% used)"
            recommendations = []

            if free_gb < 0.1:  # Less than 100MB
                status = "fail"
                message = f"Critical: Only {free_gb:.1f}GB free"
                recommendations.extend([
                    "Free up disk space immediately",
                    "Clean up old logs and temporary files",
                    "Consider increasing storage capacity"
                ])
            elif free_gb < 0.5:  # Less than 500MB
                status = "warning"
                message = f"Warning: Only {free_gb:.1f}GB free"
                recommendations.extend([
                    "Clean up unnecessary files",
                    "Monitor disk usage closely"
                ])

            return DiagnosticResult(
                check_name="disk_space",
                status=status,
                message=message,
                details={
                    "free_gb": free_gb,
                    "used_gb": disk.used / (1024**3),
                    "total_gb": disk.total / (1024**3),
                    "used_percent": used_percent
                },
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=recommendations
            )

        except Exception as e:
            return DiagnosticResult(
                check_name="disk_space",
                status="fail",
                message=f"Failed to check disk space: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Check file system permissions and disk health"]
            )

    def _check_configuration(self) -> DiagnosticResult:
        """Check configuration validity and completeness"""
        try:
            if not os.path.exists(self.config_file):
                return DiagnosticResult(
                    check_name="configuration",
                    status="fail",
                    message="Configuration file not found",
                    details={"config_file": self.config_file},
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    duration_ms=0,
                    recommendations=["Ensure AICleaner is properly configured"]
                )

            with open(self.config_file, 'r') as f:
                config = json.load(f)

            # Check required fields
            required_fields = ['gemini_api_key', 'zones']
            missing_fields = []
            invalid_fields = []

            for field in required_fields:
                if field not in config:
                    missing_fields.append(field)
                elif field == 'gemini_api_key' and not config[field]:
                    invalid_fields.append(f"{field} is empty")
                elif field == 'zones' and not isinstance(config[field], list):
                    invalid_fields.append(f"{field} must be a list")
                elif field == 'zones' and len(config[field]) == 0:
                    invalid_fields.append(f"{field} list is empty")

            # Validate zone configurations
            zone_issues = []
            if 'zones' in config and isinstance(config['zones'], list):
                for i, zone in enumerate(config['zones']):
                    if not isinstance(zone, dict):
                        zone_issues.append(f"Zone {i} is not a dictionary")
                        continue

                    required_zone_fields = ['name', 'camera_entity', 'todo_list_entity']
                    for field in required_zone_fields:
                        if field not in zone or not zone[field]:
                            zone_issues.append(f"Zone {i} missing {field}")

            # Determine status
            if missing_fields or invalid_fields or zone_issues:
                status = "fail"
                issues = missing_fields + invalid_fields + zone_issues
                message = f"Configuration issues: {'; '.join(issues)}"
                recommendations = [
                    "Fix configuration issues in the add-on configuration",
                    "Ensure all required fields are properly set",
                    "Validate zone configurations"
                ]
            else:
                status = "pass"
                message = "Configuration is valid"
                recommendations = []

            return DiagnosticResult(
                check_name="configuration",
                status=status,
                message=message,
                details={
                    "config_file_exists": True,
                    "zones_configured": len(config.get('zones', [])),
                    "has_api_key": bool(config.get('gemini_api_key')),
                    "missing_fields": missing_fields,
                    "invalid_fields": invalid_fields,
                    "zone_issues": zone_issues
                },
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=recommendations
            )

        except json.JSONDecodeError as e:
            return DiagnosticResult(
                check_name="configuration",
                status="fail",
                message=f"Configuration file is not valid JSON: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Fix JSON syntax errors in configuration"]
            )
        except Exception as e:
            return DiagnosticResult(
                check_name="configuration",
                status="fail",
                message=f"Failed to check configuration: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Check configuration file permissions and format"]
            )

    def _check_file_permissions(self) -> DiagnosticResult:
        """Check file and directory permissions"""
        try:
            issues = []
            recommendations = []

            # Check data directory permissions
            if not os.access(self.data_dir, os.R_OK | os.W_OK):
                issues.append(f"Data directory {self.data_dir} not readable/writable")
                recommendations.append("Fix data directory permissions")

            # Check config file permissions
            if os.path.exists(self.config_file):
                if not os.access(self.config_file, os.R_OK):
                    issues.append(f"Config file {self.config_file} not readable")
                    recommendations.append("Fix config file permissions")

            # Check subdirectories
            subdirs = ['cache', 'health', 'metrics', 'errors', 'analytics', 'diagnostics']
            for subdir in subdirs:
                subdir_path = os.path.join(self.data_dir, subdir)
                if os.path.exists(subdir_path):
                    if not os.access(subdir_path, os.R_OK | os.W_OK):
                        issues.append(f"Directory {subdir} not accessible")
                        recommendations.append(f"Fix {subdir} directory permissions")

            status = "fail" if issues else "pass"
            message = f"Permission issues: {'; '.join(issues)}" if issues else "File permissions are correct"

            return DiagnosticResult(
                check_name="file_permissions",
                status=status,
                message=message,
                details={
                    "data_dir_writable": os.access(self.data_dir, os.W_OK),
                    "config_file_readable": os.access(self.config_file, os.R_OK) if os.path.exists(self.config_file) else False,
                    "issues": issues
                },
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=recommendations
            )

        except Exception as e:
            return DiagnosticResult(
                check_name="file_permissions",
                status="fail",
                message=f"Failed to check file permissions: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Check system permissions and file system health"]
            )

    def _check_network_connectivity(self) -> DiagnosticResult:
        """Check network connectivity"""
        try:
            import socket
            import urllib.request

            connectivity_results = {}
            issues = []
            recommendations = []

            # Test DNS resolution
            try:
                socket.gethostbyname('google.com')
                connectivity_results['dns'] = True
            except socket.gaierror:
                connectivity_results['dns'] = False
                issues.append("DNS resolution failed")
                recommendations.append("Check DNS configuration")

            # Test internet connectivity
            try:
                urllib.request.urlopen('https://www.google.com', timeout=5)
                connectivity_results['internet'] = True
            except:
                connectivity_results['internet'] = False
                issues.append("Internet connectivity failed")
                recommendations.append("Check internet connection")

            # Test Google AI API endpoint
            try:
                urllib.request.urlopen('https://generativelanguage.googleapis.com', timeout=5)
                connectivity_results['gemini_api'] = True
            except:
                connectivity_results['gemini_api'] = False
                issues.append("Cannot reach Gemini API endpoint")
                recommendations.append("Check API endpoint accessibility")

            status = "fail" if issues else "pass"
            message = f"Network issues: {'; '.join(issues)}" if issues else "Network connectivity is good"

            return DiagnosticResult(
                check_name="network_connectivity",
                status=status,
                message=message,
                details=connectivity_results,
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=recommendations
            )

        except Exception as e:
            return DiagnosticResult(
                check_name="network_connectivity",
                status="fail",
                message=f"Failed to check network connectivity: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Check network configuration and firewall settings"]
            )

    def _check_api_endpoints(self) -> DiagnosticResult:
        """Check API endpoint accessibility and authentication"""
        try:
            # Load configuration to get API keys
            if not os.path.exists(self.config_file):
                return DiagnosticResult(
                    check_name="api_endpoints",
                    status="warning",
                    message="Cannot test API endpoints - no configuration file",
                    details={},
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    duration_ms=0,
                    recommendations=["Configure AICleaner first"]
                )

            with open(self.config_file, 'r') as f:
                config = json.load(f)

            api_results = {}
            issues = []
            recommendations = []

            # Test Gemini API
            gemini_key = config.get('gemini_api_key')
            if gemini_key and gemini_key != "AIzaSyExample_API_Key_Replace_With_Your_Key":
                try:
                    # Simple API test (would need actual implementation)
                    api_results['gemini'] = "configured"
                except:
                    api_results['gemini'] = "failed"
                    issues.append("Gemini API authentication failed")
                    recommendations.append("Check Gemini API key validity")
            else:
                api_results['gemini'] = "not_configured"
                issues.append("Gemini API key not configured")
                recommendations.append("Configure valid Gemini API key")

            # Test other APIs if configured
            for api_name in ['claude_api_key', 'openai_api_key']:
                api_key = config.get(api_name)
                if api_key:
                    api_results[api_name.replace('_api_key', '')] = "configured"
                else:
                    api_results[api_name.replace('_api_key', '')] = "not_configured"

            status = "fail" if issues else "pass"
            message = f"API issues: {'; '.join(issues)}" if issues else "API endpoints are accessible"

            return DiagnosticResult(
                check_name="api_endpoints",
                status=status,
                message=message,
                details=api_results,
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=recommendations
            )

        except Exception as e:
            return DiagnosticResult(
                check_name="api_endpoints",
                status="fail",
                message=f"Failed to check API endpoints: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Check configuration and network connectivity"]
            )

    def _check_data_integrity(self) -> DiagnosticResult:
        """Check data file integrity"""
        try:
            issues = []
            recommendations = []
            file_stats = {}

            # Check state file
            state_file = os.path.join(self.data_dir, "state.json")
            if os.path.exists(state_file):
                try:
                    with open(state_file, 'r') as f:
                        state = json.load(f)
                    file_stats['state_file'] = {
                        "exists": True,
                        "valid_json": True,
                        "size_mb": os.path.getsize(state_file) / (1024*1024)
                    }
                    if not isinstance(state, dict):
                        issues.append("State file is not a valid dictionary")
                        recommendations.append("Restore state file from backup")
                except json.JSONDecodeError:
                    issues.append("State file contains invalid JSON")
                    recommendations.append("Fix or restore state file")
                    file_stats['state_file'] = {"exists": True, "valid_json": False}
            else:
                file_stats['state_file'] = {"exists": False}

            # Check cache directory
            cache_dir = os.path.join(self.data_dir, "cache")
            if os.path.exists(cache_dir):
                cache_files = len([f for f in os.listdir(cache_dir) if f.endswith('.json')])
                file_stats['cache'] = {"exists": True, "file_count": cache_files}
            else:
                file_stats['cache'] = {"exists": False}

            # Check log files
            log_patterns = ["*.log", "aicleaner*.log"]
            log_files = []
            for pattern in log_patterns:
                log_files.extend(Path(self.data_dir).glob(pattern))

            file_stats['logs'] = {"count": len(log_files)}

            status = "fail" if issues else "pass"
            message = f"Data integrity issues: {'; '.join(issues)}" if issues else "Data integrity is good"

            return DiagnosticResult(
                check_name="data_integrity",
                status=status,
                message=message,
                details=file_stats,
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=recommendations
            )

        except Exception as e:
            return DiagnosticResult(
                check_name="data_integrity",
                status="fail",
                message=f"Failed to check data integrity: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Check file system and data directory"]
            )

    def _check_log_analysis(self) -> DiagnosticResult:
        """Analyze recent logs for issues"""
        try:
            log_analysis = {
                "error_count": 0,
                "warning_count": 0,
                "recent_errors": [],
                "patterns": []
            }

            recommendations = []

            # Find log files
            log_files = []
            for pattern in ["*.log", "aicleaner*.log"]:
                log_files.extend(Path(self.data_dir).glob(pattern))

            if not log_files:
                return DiagnosticResult(
                    check_name="log_analysis",
                    status="info",
                    message="No log files found",
                    details=log_analysis,
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    duration_ms=0,
                    recommendations=["Enable logging if needed"]
                )

            # Analyze recent log entries (last 100 lines)
            recent_lines = []
            for log_file in log_files[-3:]:  # Check last 3 log files
                try:
                    with open(log_file, 'r') as f:
                        lines = f.readlines()
                        recent_lines.extend(lines[-50:])  # Last 50 lines per file
                except:
                    continue

            # Count errors and warnings
            for line in recent_lines:
                line_lower = line.lower()
                if 'error' in line_lower:
                    log_analysis["error_count"] += 1
                    if len(log_analysis["recent_errors"]) < 5:
                        log_analysis["recent_errors"].append(line.strip())
                elif 'warning' in line_lower:
                    log_analysis["warning_count"] += 1

            # Determine status
            if log_analysis["error_count"] > 10:
                status = "fail"
                message = f"High error count in logs: {log_analysis['error_count']} errors"
                recommendations.append("Investigate and fix recurring errors")
            elif log_analysis["error_count"] > 0:
                status = "warning"
                message = f"Some errors found in logs: {log_analysis['error_count']} errors"
                recommendations.append("Review recent errors")
            else:
                status = "pass"
                message = "No significant issues in recent logs"

            return DiagnosticResult(
                check_name="log_analysis",
                status=status,
                message=message,
                details=log_analysis,
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=recommendations
            )

        except Exception as e:
            return DiagnosticResult(
                check_name="log_analysis",
                status="fail",
                message=f"Failed to analyze logs: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Check log file permissions and format"]
            )

    def _check_cache_health(self) -> DiagnosticResult:
        """Check cache system health"""
        try:
            cache_dir = os.path.join(self.data_dir, "cache")
            cache_stats = {
                "exists": os.path.exists(cache_dir),
                "file_count": 0,
                "total_size_mb": 0,
                "oldest_file_age_hours": 0,
                "newest_file_age_hours": 0
            }

            recommendations = []

            if cache_stats["exists"]:
                cache_files = [f for f in os.listdir(cache_dir) if f.endswith('.json')]
                cache_stats["file_count"] = len(cache_files)

                if cache_files:
                    file_sizes = []
                    file_ages = []
                    current_time = time.time()

                    for cache_file in cache_files:
                        file_path = os.path.join(cache_dir, cache_file)
                        file_size = os.path.getsize(file_path)
                        file_mtime = os.path.getmtime(file_path)

                        file_sizes.append(file_size)
                        file_ages.append((current_time - file_mtime) / 3600)  # hours

                    cache_stats["total_size_mb"] = sum(file_sizes) / (1024*1024)
                    cache_stats["oldest_file_age_hours"] = max(file_ages)
                    cache_stats["newest_file_age_hours"] = min(file_ages)

                    # Check for issues
                    if cache_stats["total_size_mb"] > 100:  # More than 100MB
                        recommendations.append("Cache size is large, consider cleanup")

                    if cache_stats["oldest_file_age_hours"] > 168:  # Older than 7 days
                        recommendations.append("Old cache files detected, cleanup recommended")

            status = "warning" if recommendations else "pass"
            message = "Cache issues detected" if recommendations else "Cache system is healthy"

            return DiagnosticResult(
                check_name="cache_health",
                status=status,
                message=message,
                details=cache_stats,
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=recommendations
            )

        except Exception as e:
            return DiagnosticResult(
                check_name="cache_health",
                status="fail",
                message=f"Failed to check cache health: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Check cache directory permissions"]
            )

    def _check_process_health(self) -> DiagnosticResult:
        """Check process health and resource usage"""
        try:
            process = psutil.Process()
            process_info = {
                "pid": process.pid,
                "memory_mb": process.memory_info().rss / (1024*1024),
                "cpu_percent": process.cpu_percent(),
                "num_threads": process.num_threads(),
                "create_time": datetime.fromtimestamp(process.create_time()).isoformat(),
                "status": process.status()
            }

            recommendations = []

            # Check for issues
            if process_info["memory_mb"] > 500:  # More than 500MB
                recommendations.append("High memory usage detected")

            if process_info["num_threads"] > 50:  # More than 50 threads
                recommendations.append("High thread count detected")

            status = "warning" if recommendations else "pass"
            message = "Process issues detected" if recommendations else "Process is healthy"

            return DiagnosticResult(
                check_name="process_health",
                status=status,
                message=message,
                details=process_info,
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=recommendations
            )

        except Exception as e:
            return DiagnosticResult(
                check_name="process_health",
                status="fail",
                message=f"Failed to check process health: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc).isoformat(),
                duration_ms=0,
                recommendations=["Check process monitoring tools"]
            )

    def _assess_overall_health(self, results: Dict[str, Any]) -> str:
        """Assess overall system health based on diagnostic results"""
        fail_count = len([r for r in results.values() if r["status"] == "fail"])
        warning_count = len([r for r in results.values() if r["status"] == "warning"])

        if fail_count > 0:
            return "critical"
        elif warning_count > 2:
            return "warning"
        elif warning_count > 0:
            return "attention"
        else:
            return "healthy"

    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate overall recommendations based on diagnostic results"""
        all_recommendations = []

        for result in results.values():
            if result.get("recommendations"):
                all_recommendations.extend(result["recommendations"])

        # Remove duplicates while preserving order
        unique_recommendations = []
        seen = set()
        for rec in all_recommendations:
            if rec not in seen:
                unique_recommendations.append(rec)
                seen.add(rec)

        return unique_recommendations[:10]  # Top 10 recommendations

    def _create_system_snapshot(self) -> Dict[str, Any]:
        """Create a comprehensive system state snapshot"""
        try:
            snapshot = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "system_info": {
                    "platform": os.name,
                    "python_version": os.sys.version,
                    "working_directory": os.getcwd()
                },
                "process_info": {},
                "file_system": {},
                "aicleaner_state": {},
                "configuration": {},
                "recent_logs": []
            }

            # System info
            try:
                import platform
                snapshot["system_info"].update({
                    "platform_system": platform.system(),
                    "platform_release": platform.release(),
                    "platform_machine": platform.machine()
                })
            except:
                pass

            # Process info
            try:
                process = psutil.Process()
                snapshot["process_info"] = {
                    "pid": process.pid,
                    "memory_mb": process.memory_info().rss / (1024*1024),
                    "cpu_percent": process.cpu_percent(),
                    "num_threads": process.num_threads(),
                    "uptime_seconds": time.time() - process.create_time()
                }
            except:
                pass

            # File system info
            try:
                disk = psutil.disk_usage(self.data_dir)
                snapshot["file_system"] = {
                    "data_dir": self.data_dir,
                    "free_gb": disk.free / (1024**3),
                    "used_gb": disk.used / (1024**3),
                    "total_gb": disk.total / (1024**3)
                }
            except:
                pass

            # AICleaner state
            try:
                state_file = os.path.join(self.data_dir, "state.json")
                if os.path.exists(state_file):
                    with open(state_file, 'r') as f:
                        state = json.load(f)
                    snapshot["aicleaner_state"] = {
                        "zones_count": len([k for k in state.keys() if k not in ['schema_version', 'migrated_at', 'last_backup']]),
                        "state_file_size_mb": os.path.getsize(state_file) / (1024*1024)
                    }
            except:
                pass

            # Configuration
            try:
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r') as f:
                        config = json.load(f)
                    snapshot["configuration"] = {
                        "zones_configured": len(config.get('zones', [])),
                        "has_gemini_key": bool(config.get('gemini_api_key')),
                        "multi_model_enabled": config.get('enable_multi_model_ai', False),
                        "mobile_enabled": config.get('enable_mobile_integration', False),
                        "gamification_enabled": config.get('enable_gamification', False)
                    }
            except:
                pass

            return snapshot

        except Exception as e:
            return {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": f"Failed to create system snapshot: {str(e)}"
            }

    def _save_diagnostic_report(self, report: Dict[str, Any]):
        """Save diagnostic report to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = os.path.join(self.diagnostics_dir, f"diagnostic_report_{timestamp}.json")

            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)

            self.logger.info(f"Diagnostic report saved to {report_file}")

            # Keep only last 10 reports
            report_files = sorted([f for f in os.listdir(self.diagnostics_dir) if f.startswith("diagnostic_report_")])
            if len(report_files) > 10:
                for old_file in report_files[:-10]:
                    os.remove(os.path.join(self.diagnostics_dir, old_file))

        except Exception as e:
            self.logger.error(f"Failed to save diagnostic report: {e}")

    def run_quick_diagnostic(self) -> Dict[str, Any]:
        """Run a quick diagnostic check (essential checks only)"""
        quick_checks = [
            "system_resources",
            "disk_space",
            "configuration",
            "network_connectivity"
        ]

        results = {}
        for check_name in quick_checks:
            if check_name in self.diagnostic_checks:
                try:
                    result = self.diagnostic_checks[check_name]()
                    results[check_name] = asdict(result) if isinstance(result, DiagnosticResult) else result
                except Exception as e:
                    results[check_name] = {
                        "check_name": check_name,
                        "status": "fail",
                        "message": f"Check failed: {str(e)}",
                        "details": {"error": str(e)},
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "duration_ms": 0,
                        "recommendations": []
                    }

        overall_status = self._assess_overall_health(results)

        return {
            "quick_diagnostic": {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "overall_status": overall_status,
                "checks_run": len(results)
            },
            "results": results,
            "recommendations": self._generate_recommendations(results)[:5]  # Top 5 for quick check
        }

    def get_diagnostic_history(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get diagnostic history from the last N days"""
        try:
            history = []
            cutoff_time = datetime.now() - timedelta(days=days)

            for filename in os.listdir(self.diagnostics_dir):
                if filename.startswith("diagnostic_report_") and filename.endswith(".json"):
                    file_path = os.path.join(self.diagnostics_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))

                    if file_time >= cutoff_time:
                        try:
                            with open(file_path, 'r') as f:
                                report = json.load(f)
                            history.append({
                                "filename": filename,
                                "timestamp": report.get("diagnostic_report", {}).get("timestamp"),
                                "overall_status": report.get("diagnostic_report", {}).get("overall_status"),
                                "checks_run": report.get("diagnostic_report", {}).get("checks_run"),
                                "checks_passed": report.get("diagnostic_report", {}).get("checks_passed"),
                                "checks_failed": report.get("diagnostic_report", {}).get("checks_failed")
                            })
                        except:
                            continue

            # Sort by timestamp
            history.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            return history

        except Exception as e:
            self.logger.error(f"Failed to get diagnostic history: {e}")
            return []