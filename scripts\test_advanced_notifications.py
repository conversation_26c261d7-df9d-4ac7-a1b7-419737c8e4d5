#!/usr/bin/env python3
"""
Test script for Advanced Notification system in AICleaner
Tests smart timing, personalization, multi-channel delivery, and notification rules
"""

import os
import sys
import json
import tempfile
from datetime import datetime, timezone, timedelta

# Add the aicleaner directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'aicleaner'))

def test_advanced_notification_system():
    """Test the advanced notification system"""
    print("🔔 AICleaner Advanced Notification System Test")
    print("=" * 55)
    
    try:
        from advanced_notifications import (AdvancedNotificationSystem, NotificationTemplate, 
                                          NotificationRule, NotificationChannel, NotificationPriority, 
                                          NotificationTiming)
        print("✅ Advanced notification modules imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import advanced notification modules: {e}")
        return False
    
    # Test 1: System initialization
    print("\n🚀 Test 1: System Initialization")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            notification_system = AdvancedNotificationSystem(data_path=temp_dir)
            print("✅ Advanced notification system initialized")
            print(f"   Data path: {temp_dir}")
            print(f"   Templates loaded: {len(notification_system.templates)}")
            print(f"   Rules loaded: {len(notification_system.rules)}")
            print(f"   User preferences loaded: ✓")
        except Exception as e:
            print(f"❌ Failed to initialize advanced notifications: {e}")
            return False
        
        # Test 2: Default templates and rules
        print("\n📋 Test 2: Default Templates and Rules")
        
        try:
            templates = notification_system.templates
            rules = notification_system.rules
            
            print(f"✅ Found {len(templates)} notification templates:")
            for template in templates[:3]:
                print(f"   • {template.name} ({template.category})")
                print(f"     Priority: {template.priority.value}")
                print(f"     Channels: {[ch.value for ch in template.channels]}")
                print(f"     Timing: {template.timing.value}")
            
            print(f"\n✅ Found {len(rules)} notification rules:")
            for rule in rules[:3]:
                print(f"   • {rule.name}")
                print(f"     Trigger: {rule.trigger_conditions}")
                print(f"     Template: {rule.template_id}")
                print(f"     Max frequency: {rule.max_frequency}/hour")
        except Exception as e:
            print(f"❌ Templates and rules test failed: {e}")
            return False
        
        # Test 3: User preferences
        print("\n⚙️ Test 3: User Preferences")
        
        try:
            preferences = notification_system.get_user_preferences()
            print("✅ User preferences retrieved")
            print(f"   Enabled channels: {preferences['enabled_channels']}")
            print(f"   Quiet hours: {preferences['quiet_hours_start']} - {preferences['quiet_hours_end']}")
            print(f"   Max notifications/hour: {preferences['max_notifications_per_hour']}")
            print(f"   Personalization: {preferences['personalization_enabled']}")
            print(f"   Smart timing: {preferences['smart_timing_enabled']}")
            print(f"   Do not disturb: {preferences['do_not_disturb']}")
        except Exception as e:
            print(f"❌ User preferences test failed: {e}")
            return False
        
        # Test 4: Preference updates
        print("\n🔧 Test 4: Preference Updates")
        
        try:
            updates = {
                'enabled_channels': ['home_assistant', 'mobile_push'],
                'personalization_enabled': True,
                'do_not_disturb': False,
                'max_notifications_per_hour': 10
            }
            
            success = notification_system.update_user_preferences(updates)
            if success:
                updated_prefs = notification_system.get_user_preferences()
                print("✅ Preferences updated successfully")
                print(f"   Enabled channels: {updated_prefs['enabled_channels']}")
                print(f"   Max notifications/hour: {updated_prefs['max_notifications_per_hour']}")
            else:
                print("❌ Failed to update preferences")
                return False
        except Exception as e:
            print(f"❌ Preference updates test failed: {e}")
            return False
        
        # Test 5: Send notifications
        print("\n📤 Test 5: Send Notifications")
        
        try:
            # Test task completion notification
            success1 = notification_system.send_notification(
                event_type="task_completed",
                variables={
                    "zone_name": "Kitchen",
                    "task_description": "Clean countertops",
                    "motivational_message": "Great job! Keep up the excellent work!"
                }
            )
            
            # Test achievement notification
            success2 = notification_system.send_notification(
                event_type="achievement_unlocked",
                variables={
                    "achievement_title": "First Steps",
                    "achievement_description": "Complete your first cleaning task"
                }
            )
            
            # Test streak milestone notification
            success3 = notification_system.send_notification(
                event_type="streak_milestone",
                variables={
                    "streak_days": 7
                }
            )
            
            print(f"✅ Notifications sent:")
            print(f"   Task completion: {'✓' if success1 else '✗'}")
            print(f"   Achievement unlocked: {'✓' if success2 else '✗'}")
            print(f"   Streak milestone: {'✓' if success3 else '✗'}")
            
        except Exception as e:
            print(f"❌ Send notifications test failed: {e}")
            return False
        
        # Test 6: Notification history
        print("\n📜 Test 6: Notification History")
        
        try:
            history = notification_system.get_notification_history(limit=10)
            print(f"✅ Retrieved {len(history)} notifications from history")
            
            for i, notification in enumerate(history[:3], 1):
                print(f"   {i}. {notification['title']}")
                print(f"      Message: {notification['message'][:50]}...")
                print(f"      Sent: {notification['sent_at']}")
                print(f"      Success: {'✓' if notification['success'] else '✗'}")
                print(f"      Channels: {', '.join(notification['channels'])}")
        except Exception as e:
            print(f"❌ Notification history test failed: {e}")
            return False
        
        # Test 7: Notification statistics
        print("\n📊 Test 7: Notification Statistics")
        
        try:
            stats = notification_system.get_notification_stats()
            print("✅ Notification statistics retrieved")
            print(f"   Total sent: {stats['total_sent']}")
            print(f"   Successful: {stats['successful']}")
            print(f"   Success rate: {stats['success_rate']:.1%}")
            
            if stats['by_category']:
                print("   By category:")
                for category, count in stats['by_category'].items():
                    print(f"     {category}: {count}")
            
            if stats['by_channel']:
                print("   By channel:")
                for channel, count in stats['by_channel'].items():
                    print(f"     {channel}: {count}")
        except Exception as e:
            print(f"❌ Notification statistics test failed: {e}")
            return False
        
        # Test 8: Frequency limiting
        print("\n⏱️ Test 8: Frequency Limiting")
        
        try:
            # Try to send multiple notifications rapidly
            sent_count = 0
            for i in range(10):
                success = notification_system.send_notification(
                    event_type="task_completed",
                    variables={
                        "zone_name": f"Zone_{i}",
                        "task_description": f"Task {i}",
                        "motivational_message": "Keep going!"
                    }
                )
                if success:
                    sent_count += 1
            
            print(f"✅ Frequency limiting test completed")
            print(f"   Attempted: 10 notifications")
            print(f"   Actually sent: {sent_count}")
            print(f"   Frequency limiting: {'✓' if sent_count < 10 else '✗'}")
        except Exception as e:
            print(f"❌ Frequency limiting test failed: {e}")
            return False
        
        # Test 9: Quiet hours (simulated)
        print("\n🌙 Test 9: Quiet Hours Simulation")
        
        try:
            # Update preferences to enable quiet hours
            quiet_prefs = {
                'quiet_hours_start': '22:00',
                'quiet_hours_end': '07:00'
            }
            notification_system.update_user_preferences(quiet_prefs)
            
            # Note: In a real test, we'd mock the current time to be in quiet hours
            print("✅ Quiet hours configuration updated")
            print(f"   Quiet hours: {quiet_prefs['quiet_hours_start']} - {quiet_prefs['quiet_hours_end']}")
            print("   (Actual quiet hours testing would require time mocking)")
        except Exception as e:
            print(f"❌ Quiet hours test failed: {e}")
            return False
        
        # Test 10: System status
        print("\n🔧 Test 10: System Status")
        
        try:
            status = notification_system.get_system_status()
            print("✅ System status retrieved")
            print(f"   Templates loaded: {status['templates_loaded']}")
            print(f"   Rules loaded: {status['rules_loaded']}")
            print(f"   History entries: {status['history_entries']}")
            print(f"   Enabled channels: {status['enabled_channels']}")
            print(f"   Do not disturb: {status['do_not_disturb']}")
            print(f"   Personalization: {status['personalization_enabled']}")
            print(f"   Smart timing: {status['smart_timing_enabled']}")
            print(f"   System health: {status['system_health']}")
        except Exception as e:
            print(f"❌ System status test failed: {e}")
            return False
        
        # Test 11: Data persistence
        print("\n💾 Test 11: Data Persistence")
        
        try:
            # Create new instance to test data loading
            notification_system2 = AdvancedNotificationSystem(data_path=temp_dir)
            history2 = notification_system2.get_notification_history()
            prefs2 = notification_system2.get_user_preferences()
            
            # Verify data was persisted
            if (len(history2) > 0 and 
                prefs2['max_notifications_per_hour'] == 10 and
                len(notification_system2.templates) > 0):
                print("✅ Data persistence working correctly")
                print(f"   History preserved: {len(history2)} entries")
                print(f"   Preferences preserved: ✓")
                print(f"   Templates preserved: {len(notification_system2.templates)}")
            else:
                print("⚠️ Data persistence issue detected")
        except Exception as e:
            print(f"❌ Data persistence test failed: {e}")
            return False
    
    print("\n🎉 Advanced Notification System Test Completed Successfully!")
    return True


def test_aicleaner_integration():
    """Test AICleaner integration with advanced notifications"""
    print("\n🏠 AICleaner Integration Test")
    print("=" * 40)
    
    # Test configuration with advanced notifications enabled
    test_config = {
        'gemini_api_key': os.getenv('GEMINI_API_KEY', 'test_key'),
        'enable_advanced_notifications': True,
        'display_name': 'Test User',
        'zones': [{
            'name': 'test_kitchen',
            'icon': 'mdi:chef-hat',
            'purpose': 'cooking and food preparation',
            'camera_entity': 'camera.test',
            'todo_list_entity': 'todo.test',
            'update_frequency': 30,
            'notifications_enabled': False,
            'notification_service': '',
            'notification_personality': 'default',
            'notify_on_create': False,
            'notify_on_complete': False
        }]
    }
    
    print(f"✅ Test configuration prepared")
    print(f"   Advanced notifications: {test_config['enable_advanced_notifications']}")
    print(f"   Test zone: {test_config['zones'][0]['name']}")
    
    # Test advanced notification methods would be available in AICleaner
    expected_methods = [
        'send_notification',
        'get_notification_history',
        'get_notification_stats',
        'update_notification_preferences',
        'get_notification_preferences'
    ]
    
    print(f"✅ Expected AICleaner methods: {', '.join(expected_methods)}")
    
    return True


if __name__ == "__main__":
    print("🚀 Starting AICleaner Advanced Notification System Tests")
    print("=" * 65)
    
    success = True
    
    # Run advanced notification system tests
    if not test_advanced_notification_system():
        success = False
    
    # Run AICleaner integration tests
    if not test_aicleaner_integration():
        success = False
    
    print("\n" + "=" * 65)
    if success:
        print("🎉 All tests completed successfully!")
        print("✅ Advanced notification system is ready for production use")
    else:
        print("❌ Some tests failed - please review the output above")
        sys.exit(1)
