#!/usr/bin/env python3
"""
End-to-End Workflow Verification for AICleaner
Tests the complete workflow: Camera → AI Analysis → Task Creation → Notifications
"""
import os
import sys
import time
import json
import requests
from datetime import datetime

# Add the aicleaner module to path
sys.path.append('/root/addons/Aiclean')
sys.path.append('/root/addons/Aiclean/aicleaner')

def load_env():
    """Load environment variables"""
    for env_file in ['.env.secrets', '.env.mcp']:
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '=' in line:
                        key, value = line.strip().split('=', 1)
                        if key.startswith('export '):
                            key = key[7:]
                        os.environ[key] = value.strip('"').strip("'")

class EndToEndWorkflowTester:
    """Complete workflow tester for AICleaner"""
    
    def __init__(self):
        self.ha_token = os.getenv('HA_TOKEN')
        self.supervisor_token = os.getenv('SUPERVISOR_TOKEN')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        # Use supervisor token for HA API (based on integration test results)
        self.working_token = self.supervisor_token
        self.ha_endpoint = 'http://supervisor/core/api'
        
        # Live entities
        self.camera_entity = 'camera.rowan_room_fluent'
        self.todo_entity = 'todo.rowan_room_cleaning_to_do'
        self.notify_entity = 'notify.mobile_app_drews_iphone'
        
        self.test_results = {}
    
    def step_1_camera_snapshot(self):
        """Step 1: Get camera snapshot"""
        print("📷 Step 1: Getting Camera Snapshot")
        
        try:
            snapshot_url = f"{self.ha_endpoint}/camera_proxy/{self.camera_entity}"
            response = requests.get(
                snapshot_url,
                headers={'Authorization': f'Bearer {self.working_token}'},
                timeout=15
            )
            
            if response.ok:
                snapshot_path = '/tmp/e2e_test_snapshot.jpg'
                with open(snapshot_path, 'wb') as f:
                    f.write(response.content)
                
                print(f"   ✅ Snapshot saved: {len(response.content):,} bytes")
                print(f"   📁 Path: {snapshot_path}")
                
                self.test_results['step_1'] = {
                    'success': True,
                    'snapshot_size': len(response.content),
                    'snapshot_path': snapshot_path
                }
                return snapshot_path
            else:
                print(f"   ❌ Failed: {response.status_code}")
                self.test_results['step_1'] = {'success': False, 'error': f'HTTP {response.status_code}'}
                return None
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            self.test_results['step_1'] = {'success': False, 'error': str(e)}
            return None
    
    def step_2_ai_analysis(self, snapshot_path):
        """Step 2: Perform AI analysis using optimized batch processing"""
        print("\n🤖 Step 2: AI Analysis (Optimized Batch)")
        
        try:
            from aicleaner.ai_optimizer import AIAnalysisOptimizer
            
            # Initialize AI optimizer
            optimizer = AIAnalysisOptimizer(api_key=self.gemini_api_key, cache_ttl=300)
            
            # Create test zone configuration
            zone_name = "Rowan's Room"
            zone_purpose = "Keep bedroom clean and organized"
            active_tasks = [
                {
                    'id': 'e2e_test_task_1',
                    'description': 'Make the bed',
                    'priority': 8
                },
                {
                    'id': 'e2e_test_task_2',
                    'description': 'Put toys away',
                    'priority': 6
                }
            ]
            ignore_rules = ['decorative items', 'personal belongings']
            
            # Perform batch analysis
            start_time = time.time()
            result, was_cached = optimizer.analyze_batch_optimized(
                image_path=snapshot_path,
                zone_name=zone_name,
                zone_purpose=zone_purpose,
                active_tasks=active_tasks,
                ignore_rules=ignore_rules
            )
            analysis_time = time.time() - start_time
            
            if result:
                print(f"   ✅ Analysis completed in {analysis_time:.2f}s")
                print(f"   📊 Cache hit: {'Yes' if was_cached else 'No'}")
                
                # Extract results
                completed_tasks = result.get('completed_tasks', {}).get('task_ids', [])
                new_tasks = result.get('new_tasks', {}).get('tasks', [])
                cleanliness_score = result.get('cleanliness_assessment', {}).get('score', 0)
                
                print(f"   📋 Completed tasks: {len(completed_tasks)}")
                print(f"   ➕ New tasks: {len(new_tasks)}")
                print(f"   🧹 Cleanliness score: {cleanliness_score}/10")
                
                self.test_results['step_2'] = {
                    'success': True,
                    'analysis_time': analysis_time,
                    'cache_hit': was_cached,
                    'completed_tasks_count': len(completed_tasks),
                    'new_tasks_count': len(new_tasks),
                    'cleanliness_score': cleanliness_score,
                    'new_tasks': new_tasks
                }
                return result
            else:
                print("   ❌ Analysis failed")
                self.test_results['step_2'] = {'success': False, 'error': 'No result returned'}
                return None
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            self.test_results['step_2'] = {'success': False, 'error': str(e)}
            return None
    
    def step_3_task_creation(self, analysis_result):
        """Step 3: Create tasks in HA todo list"""
        print("\n📝 Step 3: Task Creation in HA Todo List")
        
        try:
            new_tasks = analysis_result.get('new_tasks', {}).get('tasks', [])
            
            if not new_tasks:
                print("   ℹ️  No new tasks to create")
                self.test_results['step_3'] = {'success': True, 'tasks_created': 0}
                return True
            
            created_count = 0
            for task in new_tasks[:2]:  # Limit to 2 tasks for testing
                task_description = task.get('description', 'Unknown task')
                priority = task.get('priority', 5)
                
                # Add task to HA todo list
                add_url = f"{self.ha_endpoint}/services/todo/add_item"
                task_item = f"[E2E Test] {task_description} (Priority: {priority})"
                
                response = requests.post(
                    add_url,
                    headers={'Authorization': f'Bearer {self.working_token}'},
                    json={
                        'entity_id': self.todo_entity,
                        'item': task_item
                    },
                    timeout=10
                )
                
                if response.ok:
                    print(f"   ✅ Created: {task_item}")
                    created_count += 1
                else:
                    print(f"   ❌ Failed to create: {task_item}")
            
            self.test_results['step_3'] = {
                'success': created_count > 0,
                'tasks_created': created_count,
                'total_tasks': len(new_tasks)
            }
            return created_count > 0
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            self.test_results['step_3'] = {'success': False, 'error': str(e)}
            return False
    
    def step_4_notification(self, analysis_result):
        """Step 4: Send notification about analysis results"""
        print("\n📱 Step 4: Sending Notification")
        
        try:
            # Create summary message
            completed_count = len(analysis_result.get('completed_tasks', {}).get('task_ids', []))
            new_count = len(analysis_result.get('new_tasks', {}).get('tasks', []))
            cleanliness_score = analysis_result.get('cleanliness_assessment', {}).get('score', 0)
            
            message = f"🏠 AICleaner Analysis Complete!\n"
            message += f"✅ Completed tasks: {completed_count}\n"
            message += f"➕ New tasks: {new_count}\n"
            message += f"🧹 Cleanliness: {cleanliness_score}/10"
            
            # Send notification
            notify_url = f"{self.ha_endpoint}/services/notify/mobile_app_drews_iphone"
            response = requests.post(
                notify_url,
                headers={'Authorization': f'Bearer {self.working_token}'},
                json={
                    'message': message,
                    'title': 'AICleaner E2E Test'
                },
                timeout=10
            )
            
            if response.ok:
                print(f"   ✅ Notification sent successfully")
                print(f"   📱 Message: {message.replace(chr(10), ' | ')}")
                
                self.test_results['step_4'] = {
                    'success': True,
                    'message': message
                }
                return True
            else:
                print(f"   ❌ Failed: {response.status_code}")
                self.test_results['step_4'] = {'success': False, 'error': f'HTTP {response.status_code}'}
                return False
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            self.test_results['step_4'] = {'success': False, 'error': str(e)}
            return False
    
    def run_complete_workflow(self):
        """Run the complete end-to-end workflow"""
        print("🚀 AICleaner End-to-End Workflow Test")
        print("=" * 60)
        
        start_time = time.time()
        
        # Step 1: Camera snapshot
        snapshot_path = self.step_1_camera_snapshot()
        if not snapshot_path:
            return False
        
        # Step 2: AI analysis
        analysis_result = self.step_2_ai_analysis(snapshot_path)
        if not analysis_result:
            return False
        
        # Step 3: Task creation
        task_success = self.step_3_task_creation(analysis_result)
        
        # Step 4: Notification
        notify_success = self.step_4_notification(analysis_result)
        
        # Cleanup
        try:
            os.remove(snapshot_path)
        except:
            pass
        
        # Results summary
        total_time = time.time() - start_time
        
        print("\n📊 End-to-End Test Results")
        print("=" * 60)
        
        steps = ['step_1', 'step_2', 'step_3', 'step_4']
        step_names = ['Camera Snapshot', 'AI Analysis', 'Task Creation', 'Notification']
        
        passed_steps = 0
        for i, step in enumerate(steps):
            result = self.test_results.get(step, {})
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            print(f"{step_names[i]}: {status}")
            if result.get('success', False):
                passed_steps += 1
        
        print(f"\nOverall: {passed_steps}/{len(steps)} steps passed")
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Success rate: {(passed_steps/len(steps))*100:.1f}%")
        
        # Save detailed results
        final_results = {
            'timestamp': datetime.now().isoformat(),
            'total_time': total_time,
            'steps_passed': passed_steps,
            'steps_total': len(steps),
            'success_rate': (passed_steps/len(steps))*100,
            'step_results': self.test_results
        }
        
        with open('/tmp/e2e_workflow_results.json', 'w') as f:
            json.dump(final_results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: /tmp/e2e_workflow_results.json")
        
        return passed_steps == len(steps)

def main():
    """Main test function"""
    load_env()
    
    tester = EndToEndWorkflowTester()
    success = tester.run_complete_workflow()
    
    if success:
        print("\n🎉 End-to-End Workflow Test: PASSED")
        print("   All systems working correctly!")
    else:
        print("\n⚠️  End-to-End Workflow Test: FAILED")
        print("   Check individual step results for details")
    
    return success

if __name__ == "__main__":
    main()
