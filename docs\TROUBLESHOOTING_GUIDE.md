# AICleaner v2.0 Troubleshooting Guide

Comprehensive troubleshooting guide for common issues, error messages, and resolution steps.

## 🚨 Quick Diagnostics

### Health Check Steps

1. **Check Add-on Status**
   ```
   Settings > Add-ons > AICleaner v2.0 > Info tab
   Status should show: "Started"
   ```

2. **Review Logs**
   ```
   Settings > Add-ons > AICleaner v2.0 > Log tab
   Look for: "AICleaner v2.0 initialized successfully"
   ```

3. **Verify Configuration**
   ```
   Settings > Add-ons > AICleaner v2.0 > Configuration tab
   Ensure all required fields are filled
   ```

4. **Test Service Call**
   ```yaml
   service: aicleaner.run_optimized_analysis
   data:
     zone_name: "YourZoneName"
   ```

---

## 🔧 Common Issues & Solutions

### 1. Add-on Won't Start

**Symptoms:**
- Add-on status shows "Stopped" or "Error"
- Logs show initialization errors

**Common Causes & Solutions:**

#### Missing API Key
```
ERROR: Configuration validation failed: gemini_api_key is required
```
**Solution:**
1. Get API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Add to Configuration tab: `gemini_api_key: "AIza..."`
3. Restart add-on

#### Invalid Zone Configuration
```
ERROR: Zone configuration validation failed
```
**Solution:**
```yaml
zones:
  - name: "Kitchen"  # Required: unique zone name
    camera_entity: "camera.kitchen_cam"  # Required: valid camera entity
    todo_entity: "todo.kitchen_cleaning"  # Required: valid todo entity
    notify_entity: "notify.mobile_app_phone"  # Required: valid notify entity
    schedule_hours: 24  # Required: positive number
```

#### Home Assistant Token Issues
```
ERROR: HA API authentication failed
```
**Solution:**
1. Create long-lived access token: `Settings > People > Security > Long-lived access tokens`
2. Add to configuration: `ha_token: "eyJ0..."`
3. Ensure token has admin privileges

---

### 2. Camera Issues

#### Camera Entity Not Found
```
ERROR: Camera entity camera.kitchen_cam not found
```
**Solution:**
1. Verify camera entity exists: `Settings > Devices & Services > Entities`
2. Check entity ID spelling in configuration
3. Ensure camera is online and accessible

#### Camera Snapshot Fails
```
ERROR: Failed to get camera snapshot: HTTP 404
```
**Solution:**
1. Test camera manually: `Developer Tools > Services > camera.snapshot`
2. Check camera permissions and network connectivity
3. Verify camera supports snapshot functionality

#### Empty/Corrupted Snapshots
```
WARNING: Received empty snapshot (0 bytes)
```
**Solution:**
1. Check camera resolution settings (recommended: 1080p or higher)
2. Verify adequate lighting in the room
3. Test snapshot at different times of day

---

### 3. AI Analysis Issues

#### Gemini API Errors
```
ERROR: Gemini API request failed: 401 Unauthorized
```
**Solution:**
1. Verify API key is correct and active
2. Check API quota: [Google Cloud Console](https://console.cloud.google.com/)
3. Ensure Gemini API is enabled for your project

#### Analysis Timeout
```
ERROR: AI analysis timed out after 30 seconds
```
**Solution:**
1. Check internet connectivity
2. Try optimized analysis: `aicleaner.run_optimized_analysis`
3. Reduce image size or improve lighting

#### Invalid Analysis Response
```
ERROR: Failed to parse AI analysis JSON
```
**Solution:**
1. Clear cache: `aicleaner.clear_cache`
2. Retry analysis with fresh request
3. Check for API rate limiting

---

### 4. Task Management Issues

#### Todo Entity Not Found
```
ERROR: Todo entity todo.kitchen_cleaning not found
```
**Solution:**
1. Create todo list: `Settings > Devices & Services > Helpers > Create Helper > To-do list`
2. Verify entity ID in configuration
3. Ensure todo integration is enabled

#### Tasks Not Creating
```
WARNING: Failed to add task to todo list
```
**Solution:**
1. Check todo entity permissions
2. Verify HA token has write access
3. Test manual task creation: `Developer Tools > Services > todo.add_item`

#### Duplicate Tasks
```
INFO: Task already exists, skipping creation
```
**Solution:**
1. This is normal behavior (prevents duplicates)
2. Clear completed tasks manually if needed
3. Adjust ignore rules if too restrictive

---

### 5. Notification Issues

#### Notifications Not Sending
```
ERROR: Failed to send notification to notify.mobile_app_phone
```
**Solution:**
1. Verify mobile app is connected: `Settings > Devices & Services > Mobile App`
2. Check notification entity ID
3. Test manual notification: `Developer Tools > Services > notify.mobile_app_phone`

#### Notification Format Issues
```
WARNING: Notification personality formatting failed
```
**Solution:**
1. Check personality configuration in zone settings
2. Verify notification templates are valid
3. Use default personality if custom fails

---

### 6. Performance Issues

#### Slow Analysis Times
```
INFO: Analysis completed in 8.5 seconds (expected: ~3s)
```
**Solution:**
1. Use optimized analysis: `aicleaner.run_optimized_analysis`
2. Enable caching (default: 5 minutes TTL)
3. Check network latency to Gemini API
4. Reduce image resolution if very high

#### High Memory Usage
```
WARNING: Memory usage above 80%
```
**Solution:**
1. Clear cache periodically: `aicleaner.clear_cache`
2. Reduce number of concurrent zones
3. Increase cache TTL to reduce API calls

#### Cache Issues
```
ERROR: Cache corruption detected, clearing cache
```
**Solution:**
1. Cache will auto-clear and rebuild
2. Monitor for recurring issues
3. Check available disk space

---

## 🔍 Advanced Debugging

### Enable Debug Logging

Add to configuration:
```yaml
logging_level: "DEBUG"
```

**Debug Log Examples:**
```
DEBUG: Cache hit for key: batch_abc123...
DEBUG: AI analysis prompt: 1,234 characters
DEBUG: Generated 3 new tasks for zone Kitchen
DEBUG: Cache cleanup removed 5 expired entries
```

### Performance Monitoring

**Check Analysis Performance:**
```yaml
service: aicleaner.run_optimized_analysis
data:
  zone_name: "Kitchen"
```

**Monitor Logs For:**
- Analysis time (should be 2-4 seconds)
- Cache hit rate (higher is better)
- Error frequency (should be minimal)

### Network Diagnostics

**Test HA API Connectivity:**
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://supervisor/core/api/states
```

**Test Gemini API:**
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://generativelanguage.googleapis.com/v1/models
```

---

## 📊 Error Code Reference

| Code | Description | Severity | Action |
|------|-------------|----------|---------|
| `CONFIG_ERROR` | Configuration validation failed | Critical | Fix configuration |
| `CAMERA_UNAVAILABLE` | Camera entity not accessible | High | Check camera |
| `API_TIMEOUT` | Gemini API request timeout | Medium | Retry/check network |
| `INVALID_RESPONSE` | AI analysis parsing failed | Medium | Clear cache/retry |
| `NETWORK_ERROR` | Network connectivity issues | High | Check connectivity |
| `RATE_LIMITED` | API rate limit exceeded | Medium | Wait/upgrade quota |
| `TASK_CREATION_FAILED` | Todo list update failed | Low | Check permissions |
| `NOTIFICATION_FAILED` | Notification send failed | Low | Check notify entity |

---

## 🆘 Getting Help

### Before Reporting Issues

1. **Check Logs:** Review full add-on logs for error details
2. **Test Configuration:** Verify all entities exist and are accessible
3. **Try Manual Service:** Test `aicleaner.run_optimized_analysis` manually
4. **Check Network:** Ensure internet connectivity and API access

### Information to Include

When reporting issues, include:

```yaml
# System Information
Home Assistant Version: 2024.x.x
AICleaner Version: 2.0.0
Installation Type: Home Assistant OS/Supervised/Container

# Configuration (sanitized)
zones:
  - name: "Kitchen"
    camera_entity: "camera.kitchen_cam"
    # ... other settings

# Error Logs
[Paste relevant log entries here]

# Steps to Reproduce
1. Step one
2. Step two
3. Error occurs
```

### Support Channels

- **GitHub Issues:** [Report bugs](https://github.com/sporebattyl/Aiclean/issues)
- **GitHub Discussions:** [Get help](https://github.com/sporebattyl/Aiclean/discussions)
- **Documentation:** [Complete guides](docs/README.md)

---

## ✅ Verification Checklist

After resolving issues, verify:

- [ ] Add-on starts successfully
- [ ] All zones show "active" status
- [ ] Camera snapshots work
- [ ] AI analysis completes (2-4 seconds)
- [ ] Tasks create in todo lists
- [ ] Notifications send to mobile
- [ ] Lovelace card displays data
- [ ] No errors in logs

---

*For additional help, see the [API Documentation](API_DOCUMENTATION.md) and [Configuration Guide](../CONFIGURATION_GUIDE.md).*
