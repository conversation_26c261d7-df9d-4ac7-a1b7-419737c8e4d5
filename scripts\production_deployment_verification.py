#!/usr/bin/env python3
"""
Production Deployment Verification for AICleaner v2.0
Comprehensive verification of all systems in production environment
"""
import os
import sys
import time
import json
import requests
from datetime import datetime

# Add the aicleaner module to path
sys.path.append('/root/addons/Aiclean')
sys.path.append('/root/addons/Aiclean/aicleaner')

def load_env():
    """Load environment variables"""
    for env_file in ['.env.secrets', '.env.mcp']:
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '=' in line:
                        key, value = line.strip().split('=', 1)
                        if key.startswith('export '):
                            key = key[7:]
                        os.environ[key] = value.strip('"').strip("'")

class ProductionVerifier:
    """Production deployment verification"""
    
    def __init__(self):
        self.supervisor_token = os.getenv('SUPERVISOR_TOKEN')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.ha_endpoint = 'http://supervisor/core/api'
        
        # Production entities
        self.camera_entity = 'camera.rowan_room_fluent'
        self.todo_entity = 'todo.rowan_room_cleaning_to_do'
        self.notify_entity = 'notify.mobile_app_drews_iphone'
        
        self.verification_results = {}
    
    def verify_addon_status(self):
        """Verify AICleaner addon is running in production"""
        print("🏠 Verification 1: Add-on Status")
        
        try:
            # Check if addon is accessible via supervisor API
            addon_url = f"http://supervisor/addons/local_aicleaner"
            response = requests.get(
                addon_url,
                headers={'Authorization': f'Bearer {self.supervisor_token}'},
                timeout=10
            )
            
            if response.ok:
                addon_info = response.json()
                state = addon_info.get('data', {}).get('state', 'unknown')
                version = addon_info.get('data', {}).get('version', 'unknown')
                
                print(f"   ✅ Add-on state: {state}")
                print(f"   ✅ Add-on version: {version}")
                
                if state == 'started':
                    self.verification_results['addon_status'] = {
                        'success': True,
                        'state': state,
                        'version': version
                    }
                    return True
                else:
                    print(f"   ❌ Add-on not started: {state}")
            else:
                print(f"   ⚠️  Add-on API not accessible: {response.status_code}")
                
        except Exception as e:
            print(f"   ⚠️  Add-on check error: {e}")
        
        # Fallback: Check if AICleaner services are available
        try:
            services_url = f"{self.ha_endpoint}/services"
            response = requests.get(
                services_url,
                headers={'Authorization': f'Bearer {self.supervisor_token}'},
                timeout=10
            )
            
            if response.ok:
                services = response.json()
                aicleaner_services = [
                    service for service in services
                    if service.get('domain') == 'aicleaner'
                ]
                
                if aicleaner_services:
                    print(f"   ✅ AICleaner services available: {len(aicleaner_services)}")
                    self.verification_results['addon_status'] = {
                        'success': True,
                        'services_count': len(aicleaner_services)
                    }
                    return True
                    
        except Exception as e:
            print(f"   ❌ Services check error: {e}")
        
        self.verification_results['addon_status'] = {'success': False}
        return False
    
    def verify_entities_integration(self):
        """Verify all required entities are working"""
        print("\n📊 Verification 2: Entity Integration")
        
        entities_to_check = [
            self.camera_entity,
            self.todo_entity,
            self.notify_entity
        ]
        
        working_entities = 0
        
        for entity_id in entities_to_check:
            try:
                state_url = f"{self.ha_endpoint}/states/{entity_id}"
                response = requests.get(
                    state_url,
                    headers={'Authorization': f'Bearer {self.supervisor_token}'},
                    timeout=10
                )
                
                if response.ok:
                    entity_state = response.json()
                    state = entity_state.get('state')
                    last_updated = entity_state.get('last_updated')
                    
                    print(f"   ✅ {entity_id}: {state} (updated: {last_updated[:19]})")
                    working_entities += 1
                else:
                    print(f"   ❌ {entity_id}: Not accessible ({response.status_code})")
                    
            except Exception as e:
                print(f"   ❌ {entity_id}: Error - {e}")
        
        success = working_entities == len(entities_to_check)
        self.verification_results['entities_integration'] = {
            'success': success,
            'working_entities': working_entities,
            'total_entities': len(entities_to_check)
        }
        
        print(f"   📊 Entity Status: {working_entities}/{len(entities_to_check)} working")
        return success
    
    def verify_optimized_analysis(self):
        """Verify optimized AI analysis is working"""
        print("\n🤖 Verification 3: Optimized AI Analysis")
        
        try:
            from aicleaner.ai_optimizer import AIAnalysisOptimizer
            
            # Initialize optimizer
            optimizer = AIAnalysisOptimizer(api_key=self.gemini_api_key, cache_ttl=300)
            
            # Get a test image
            snapshot_url = f"{self.ha_endpoint}/camera_proxy/{self.camera_entity}"
            response = requests.get(
                snapshot_url,
                headers={'Authorization': f'Bearer {self.supervisor_token}'},
                timeout=15
            )
            
            if response.ok:
                test_image_path = '/tmp/production_verification_snapshot.jpg'
                with open(test_image_path, 'wb') as f:
                    f.write(response.content)
                
                print(f"   ✅ Test snapshot: {len(response.content):,} bytes")
                
                # Perform optimized analysis
                start_time = time.time()
                result, was_cached = optimizer.analyze_batch_optimized(
                    image_path=test_image_path,
                    zone_name="Production Test Zone",
                    zone_purpose="Verify production deployment",
                    active_tasks=[],
                    ignore_rules=[]
                )
                analysis_time = time.time() - start_time
                
                if result:
                    print(f"   ✅ Analysis completed: {analysis_time:.2f}s")
                    print(f"   ✅ Cache hit: {'Yes' if was_cached else 'No'}")
                    
                    # Test cache performance
                    start_time = time.time()
                    result2, was_cached2 = optimizer.analyze_batch_optimized(
                        image_path=test_image_path,
                        zone_name="Production Test Zone",
                        zone_purpose="Verify production deployment",
                        active_tasks=[],
                        ignore_rules=[]
                    )
                    cache_time = time.time() - start_time
                    
                    if was_cached2:
                        speedup = analysis_time / cache_time if cache_time > 0 else float('inf')
                        print(f"   ✅ Cache speedup: {speedup:.0f}x faster")
                    
                    # Cleanup
                    os.remove(test_image_path)
                    
                    self.verification_results['optimized_analysis'] = {
                        'success': True,
                        'analysis_time': analysis_time,
                        'cache_working': was_cached2,
                        'speedup': speedup if was_cached2 else 1
                    }
                    return True
                else:
                    print("   ❌ Analysis failed")
            else:
                print(f"   ❌ Snapshot failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Analysis error: {e}")
        
        self.verification_results['optimized_analysis'] = {'success': False}
        return False
    
    def verify_lovelace_card(self):
        """Verify Lovelace card is accessible"""
        print("\n🃏 Verification 4: Lovelace Card")
        
        card_urls = [
            'http://***********:8099/aicleaner-card.js',
            'http://localhost:8099/aicleaner-card.js'
        ]
        
        for url in card_urls:
            try:
                response = requests.get(url, timeout=10)
                
                if response.ok:
                    content_length = len(response.content)
                    content_type = response.headers.get('Content-Type', 'unknown')
                    
                    print(f"   ✅ Card accessible: {url}")
                    print(f"   ✅ Size: {content_length:,} bytes")
                    print(f"   ✅ Type: {content_type}")
                    
                    self.verification_results['lovelace_card'] = {
                        'success': True,
                        'url': url,
                        'size': content_length,
                        'content_type': content_type
                    }
                    return True
                    
            except Exception as e:
                print(f"   ❌ {url}: {str(e)[:50]}...")
        
        self.verification_results['lovelace_card'] = {'success': False}
        return False
    
    def verify_end_to_end_workflow(self):
        """Verify complete end-to-end workflow"""
        print("\n🔄 Verification 5: End-to-End Workflow")
        
        try:
            # Step 1: Trigger analysis via service call
            service_url = f"{self.ha_endpoint}/services/aicleaner/run_optimized_analysis"
            
            response = requests.post(
                service_url,
                headers={'Authorization': f'Bearer {self.supervisor_token}'},
                json={},
                timeout=30
            )
            
            if response.ok:
                print("   ✅ Service call successful")
                
                # Step 2: Wait for analysis to complete
                time.sleep(5)
                
                # Step 3: Check if new tasks were created
                todo_state_url = f"{self.ha_endpoint}/states/{self.todo_entity}"
                todo_response = requests.get(
                    todo_state_url,
                    headers={'Authorization': f'Bearer {self.supervisor_token}'},
                    timeout=10
                )
                
                if todo_response.ok:
                    todo_state = todo_response.json()
                    current_items = len(todo_state.get('attributes', {}).get('items', []))
                    print(f"   ✅ Todo list accessible: {current_items} items")
                    
                    # Step 4: Send test notification
                    notify_url = f"{self.ha_endpoint}/services/notify/mobile_app_drews_iphone"
                    notify_response = requests.post(
                        notify_url,
                        headers={'Authorization': f'Bearer {self.supervisor_token}'},
                        json={
                            'message': 'AICleaner v2.0 Production Verification Complete!',
                            'title': 'Production Deployment Success'
                        },
                        timeout=10
                    )
                    
                    if notify_response.ok:
                        print("   ✅ Notification sent successfully")
                        
                        self.verification_results['end_to_end'] = {
                            'success': True,
                            'service_call': True,
                            'todo_accessible': True,
                            'notification_sent': True
                        }
                        return True
                    else:
                        print(f"   ❌ Notification failed: {notify_response.status_code}")
                else:
                    print(f"   ❌ Todo check failed: {todo_response.status_code}")
            else:
                print(f"   ❌ Service call failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Workflow error: {e}")
        
        self.verification_results['end_to_end'] = {'success': False}
        return False
    
    def run_production_verification(self):
        """Run complete production verification"""
        print("🚀 AICleaner v2.0 Production Deployment Verification")
        print("=" * 70)
        
        start_time = time.time()
        
        # Run all verifications
        verifications = [
            self.verify_addon_status,
            self.verify_entities_integration,
            self.verify_optimized_analysis,
            self.verify_lovelace_card,
            self.verify_end_to_end_workflow
        ]
        
        verification_names = [
            'Add-on Status',
            'Entity Integration',
            'Optimized Analysis',
            'Lovelace Card',
            'End-to-End Workflow'
        ]
        
        results = []
        for verification in verifications:
            results.append(verification())
        
        # Summary
        total_time = time.time() - start_time
        passed_verifications = sum(results)
        
        print("\n📊 Production Verification Results")
        print("=" * 70)
        
        for i, (name, passed) in enumerate(zip(verification_names, results)):
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{name}: {status}")
        
        print(f"\nOverall: {passed_verifications}/{len(results)} verifications passed")
        print(f"Success rate: {(passed_verifications/len(results))*100:.1f}%")
        print(f"Verification time: {total_time:.2f} seconds")
        
        # Save results
        final_results = {
            'timestamp': datetime.now().isoformat(),
            'verifications_passed': passed_verifications,
            'verifications_total': len(results),
            'success_rate': (passed_verifications/len(results))*100,
            'verification_time': total_time,
            'production_ready': passed_verifications == len(results),
            'detailed_results': self.verification_results
        }
        
        with open('/tmp/production_verification_results.json', 'w') as f:
            json.dump(final_results, f, indent=2)
        
        print(f"\n💾 Results saved to: /tmp/production_verification_results.json")
        
        if passed_verifications == len(results):
            print("\n🎉 PRODUCTION DEPLOYMENT VERIFIED!")
            print("   AICleaner v2.0 is fully operational in production!")
        else:
            print("\n⚠️  PRODUCTION DEPLOYMENT ISSUES DETECTED")
            print("   Some verifications failed - check results above")
        
        return passed_verifications == len(results)

def main():
    """Main verification function"""
    load_env()
    
    verifier = ProductionVerifier()
    success = verifier.run_production_verification()
    
    return success

if __name__ == "__main__":
    main()
