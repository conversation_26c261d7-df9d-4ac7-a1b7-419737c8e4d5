name: "AICleaner v2.0+"
version: "2.0.1"
slug: "aicleaner"
description: "Intelligent multi-zone cleanliness management with AI-powered analysis, mobile optimization, gamification, and smart notifications."
url: "https://github.com/sporebattyl/Aiclean"
arch:
  - aarch64
  - amd64
  - armv7
startup: "application"
boot: "auto"
init: false
homeassistant_api: true
ports:
  8099/tcp: 8099
ports_description:
  8099/tcp: "Static file server for Lovelace cards"
map:
  - "config:ro"
  - "share:rw"
  - "ssl:ro"
  - "media:rw"
  - "addons:rw"
  - "backup:ro"

options:
  gemini_api_key: "AIzaSyExample_API_Key_Replace_With_Your_Key"
  display_name: "User"

  # Advanced AI Configuration
  enable_multi_model_ai: false
  claude_api_key: ""
  openai_api_key: ""
  enable_predictive_analytics: true
  enable_scene_understanding: true

  # Mobile Integration Configuration
  enable_mobile_integration: true
  enable_pwa_features: true
  mobile_push_notifications: true
  mobile_location_awareness: false
  mobile_voice_commands: false
  mobile_quick_actions: true
  mobile_offline_mode: false
  mobile_gesture_controls: true
  mobile_notification_sound: "default"
  mobile_vibration_pattern: "default"
  mobile_theme_preference: "auto"  # auto, light, dark
  mobile_compact_mode: false

  # Gamification System Configuration
  enable_gamification: true
  gamification_achievements: true
  gamification_daily_challenges: true
  gamification_experience_points: true
  gamification_leveling_system: true
  gamification_streaks: true
  gamification_leaderboards: false
  gamification_social_features: false

  # Advanced Notifications Configuration
  enable_advanced_notifications: true
  notification_smart_timing: true
  notification_personalization: true
  notification_quiet_hours_start: "22:00"
  notification_quiet_hours_end: "07:00"
  notification_max_frequency: 5  # Max per hour
  notification_cooldown_minutes: 15
  notification_channels:
    - "home_assistant"
    - "mobile_push"
  notification_do_not_disturb: false

  zones:
    - name: "kitchen"
      icon: "mdi:chef-hat"
      purpose: "Keep the kitchen clean and organized"
      camera_entity: "camera.kitchen"
      todo_list_entity: "todo.kitchen_tasks"
      update_frequency: 30
      notifications_enabled: true
      notification_service: "mobile_app_your_phone"
      notification_personality: "default"
      notify_on_create: true
      notify_on_complete: true

schema:
  gemini_api_key: password
  display_name: str

  # Advanced AI Configuration
  enable_multi_model_ai: bool
  claude_api_key: password?
  openai_api_key: password?
  enable_predictive_analytics: bool
  enable_scene_understanding: bool

  # Mobile Integration Configuration
  enable_mobile_integration: bool
  enable_pwa_features: bool
  mobile_push_notifications: bool
  mobile_location_awareness: bool
  mobile_voice_commands: bool
  mobile_quick_actions: bool
  mobile_offline_mode: bool
  mobile_gesture_controls: bool
  mobile_notification_sound: list(default|chime|bell|alert|custom)
  mobile_vibration_pattern: list(default|short|long|double|custom)
  mobile_theme_preference: list(auto|light|dark)
  mobile_compact_mode: bool

  # Gamification System Configuration
  enable_gamification: bool
  gamification_achievements: bool
  gamification_daily_challenges: bool
  gamification_experience_points: bool
  gamification_leveling_system: bool
  gamification_streaks: bool
  gamification_leaderboards: bool
  gamification_social_features: bool

  # Advanced Notifications Configuration
  enable_advanced_notifications: bool
  notification_smart_timing: bool
  notification_personalization: bool
  notification_quiet_hours_start: str
  notification_quiet_hours_end: str
  notification_max_frequency: int(1,20)
  notification_cooldown_minutes: int(1,1440)
  notification_channels:
    - list(home_assistant|mobile_push|email|sms|webhook)
  notification_do_not_disturb: bool

  zones:
    - name: str
      icon: str
      purpose: str
      camera_entity: str
      todo_list_entity: str
      update_frequency: int(1,168)
      notifications_enabled: bool
      notification_service: str?
      notification_personality: list(default|snarky|jarvis|roaster|butler|coach|zen)
      notify_on_create: bool
      notify_on_complete: bool

      # Optional: Monitor AICleaner status
sensor:
  - platform: file
    name: "AICleaner Status"
    file_path: "/tmp/aicleaner_triggers/status.json"
    value_template: "{{ value_json.status }}"
    json_attributes:
      - message
      - timestamp
      - zones