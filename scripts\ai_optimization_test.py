#!/usr/bin/env python3
"""
AI Analysis Performance Optimization Testing
"""
import os
import sys
import time
import json
import hashlib
from datetime import datetime, timedelta
import google.generativeai as genai
from PIL import Image

def load_env():
    """Load environment variables"""
    for env_file in ['.env.secrets', '.env.mcp']:
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '=' in line:
                        key, value = line.strip().split('=', 1)
                        if key.startswith('export '):
                            key = key[7:]
                        os.environ[key] = value.strip('"').strip("'")

class AIAnalysisOptimizer:
    """Optimized AI analysis with caching and batching"""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes cache TTL
        
        # Configure Gemini
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        genai.configure(api_key=gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
    
    def _get_image_hash(self, image_path):
        """Generate hash for image caching"""
        with open(image_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    
    def _is_cache_valid(self, cache_entry):
        """Check if cache entry is still valid"""
        if not cache_entry:
            return False
        
        cache_time = datetime.fromisoformat(cache_entry['timestamp'])
        return datetime.now() - cache_time < timedelta(seconds=self.cache_ttl)
    
    def _get_cached_result(self, cache_key):
        """Get cached result if valid"""
        if cache_key in self.cache:
            entry = self.cache[cache_key]
            if self._is_cache_valid(entry):
                return entry['result']
        return None
    
    def _cache_result(self, cache_key, result):
        """Cache analysis result"""
        self.cache[cache_key] = {
            'result': result,
            'timestamp': datetime.now().isoformat()
        }
    
    def analyze_single(self, image_path, prompt, analysis_type):
        """Single analysis with caching"""
        # Generate cache key
        image_hash = self._get_image_hash(image_path)
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()
        cache_key = f"{analysis_type}_{image_hash}_{prompt_hash}"
        
        # Check cache first
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            return cached_result, True  # True indicates cache hit
        
        # Perform analysis
        with Image.open(image_path) as img:
            response = self.model.generate_content([prompt, img])
            result = response.text
            
            # Cache the result
            self._cache_result(cache_key, result)
            
            return result, False  # False indicates cache miss
    
    def analyze_batch(self, image_path, prompts_dict):
        """Batch analysis with optimized prompting"""
        # Create combined prompt for batch processing
        combined_prompt = """Analyze this room image and provide responses for multiple analysis types.

Please provide a JSON response with the following structure:
{
  "completed_tasks": {
    "tasks": ["task1", "task2"],
    "confidence": 0.85,
    "notes": "explanation"
  },
  "new_tasks": {
    "tasks": [{"description": "task", "priority": 7, "category": "cleaning"}],
    "room_cleanliness_score": 6,
    "notes": "explanation"
  },
  "cleanliness_assessment": {
    "score": 7,
    "state": "moderately_clean",
    "observations": ["obs1", "obs2"],
    "recommendations": ["rec1"]
  }
}

Analyze for:
1. Completed cleaning tasks (what has been done recently)
2. New tasks needed (what needs to be done)
3. Overall cleanliness assessment (current state)"""
        
        # Check if we have cached batch result
        image_hash = self._get_image_hash(image_path)
        cache_key = f"batch_{image_hash}"
        
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            return cached_result, True
        
        # Perform batch analysis
        with Image.open(image_path) as img:
            response = self.model.generate_content([combined_prompt, img])
            result = response.text
            
            # Cache the result
            self._cache_result(cache_key, result)
            
            return result, False

def test_optimization_performance():
    """Test AI optimization performance"""
    load_env()
    
    print("🚀 AI Analysis Performance Optimization Test")
    print("=" * 60)
    
    optimizer = AIAnalysisOptimizer()
    image_path = '/tmp/live_camera_performance_test.jpg'
    
    if not os.path.exists(image_path):
        print("❌ Test image not found. Run camera performance test first.")
        return
    
    # Define analysis prompts
    prompts = {
        'completed_tasks': '''Analyze this room image for completed cleaning tasks. Look for signs that cleaning tasks have been completed recently.

Return a JSON response with this structure:
{
  "completed_tasks": ["task_description_1", "task_description_2"],
  "confidence": 0.85,
  "analysis_notes": "Brief explanation of what you observed"
}''',
        
        'new_tasks': '''Analyze this room image and identify cleaning tasks that need to be done. Focus on visible mess, clutter, or areas that need attention.

Return a JSON response with this structure:
{
  "new_tasks": [
    {"description": "task_description", "priority": 7, "category": "cleaning"}
  ],
  "room_cleanliness_score": 6,
  "analysis_notes": "Brief explanation"
}''',
        
        'cleanliness': '''Analyze the overall cleanliness of this room. Rate it on a scale of 1-10 and provide specific observations.

Return a JSON response with this structure:
{
  "cleanliness_score": 7,
  "cleanliness_state": "moderately_clean",
  "observations": ["specific_observation_1", "specific_observation_2"],
  "recommendations": ["recommendation_1"]
}'''
    }
    
    # Test 1: Individual Analysis (Current Method)
    print("\n🔍 Test 1: Individual Analysis (Current Method)")
    start_time = time.time()
    
    individual_results = {}
    cache_hits = 0
    
    for analysis_type, prompt in prompts.items():
        analysis_start = time.time()
        result, was_cached = optimizer.analyze_single(image_path, prompt, analysis_type)
        analysis_time = time.time() - analysis_start
        
        individual_results[analysis_type] = {
            'result': result,
            'time': analysis_time,
            'cached': was_cached
        }
        
        if was_cached:
            cache_hits += 1
            
        print(f"   {analysis_type}: {analysis_time:.2f}s {'(cached)' if was_cached else '(fresh)'}")
    
    individual_total_time = time.time() - start_time
    print(f"   Total time: {individual_total_time:.2f}s")
    print(f"   Cache hits: {cache_hits}/3")
    
    # Test 2: Batch Analysis (Optimized Method)
    print("\n🔍 Test 2: Batch Analysis (Optimized Method)")
    start_time = time.time()
    
    batch_result, was_cached = optimizer.analyze_batch(image_path, prompts)
    batch_time = time.time() - start_time
    
    print(f"   Batch analysis: {batch_time:.2f}s {'(cached)' if was_cached else '(fresh)'}")
    
    # Test 3: Cache Performance (Second Run)
    print("\n🔍 Test 3: Cache Performance Test (Second Run)")
    
    # Individual with cache
    start_time = time.time()
    cache_hits = 0
    for analysis_type, prompt in prompts.items():
        result, was_cached = optimizer.analyze_single(image_path, prompt, analysis_type)
        if was_cached:
            cache_hits += 1
    individual_cached_time = time.time() - start_time
    
    # Batch with cache
    start_time = time.time()
    batch_result, batch_was_cached = optimizer.analyze_batch(image_path, prompts)
    batch_cached_time = time.time() - start_time
    
    print(f"   Individual (cached): {individual_cached_time:.2f}s (hits: {cache_hits}/3)")
    print(f"   Batch (cached): {batch_cached_time:.2f}s {'(hit)' if batch_was_cached else '(miss)'}")
    
    # Performance Summary
    print("\n📊 Performance Summary")
    print("=" * 60)
    
    if not was_cached:  # Only compare fresh results
        improvement = ((individual_total_time - batch_time) / individual_total_time) * 100
        print(f"🚀 Batch optimization: {improvement:.1f}% faster than individual")
        print(f"   Individual: {individual_total_time:.2f}s")
        print(f"   Batch: {batch_time:.2f}s")
        print(f"   Savings: {individual_total_time - batch_time:.2f}s")
    
    cache_improvement = ((individual_total_time - individual_cached_time) / individual_total_time) * 100
    print(f"⚡ Cache optimization: {cache_improvement:.1f}% faster")
    print(f"   Fresh: {individual_total_time:.2f}s")
    print(f"   Cached: {individual_cached_time:.2f}s")
    
    # Save optimization results
    results = {
        'timestamp': datetime.now().isoformat(),
        'individual_analysis': {
            'time': individual_total_time,
            'cache_hits': cache_hits
        },
        'batch_analysis': {
            'time': batch_time,
            'cached': was_cached
        },
        'cache_performance': {
            'individual_cached_time': individual_cached_time,
            'batch_cached_time': batch_cached_time
        },
        'optimizations': {
            'batch_improvement_percent': improvement if not was_cached else None,
            'cache_improvement_percent': cache_improvement
        }
    }
    
    with open('/tmp/ai_optimization_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: /tmp/ai_optimization_results.json")
    
    return results

if __name__ == "__main__":
    test_optimization_performance()
