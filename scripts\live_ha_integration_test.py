#!/usr/bin/env python3
"""
Live Home Assistant Integration Testing
Tests with real entities: camera.rowan_room_fluent, todo.rowan_room_cleaning_to_do, notify.mobile_app_drews_iphone
"""
import os
import sys
import time
import json
import requests
from datetime import datetime

def load_env():
    """Load environment variables"""
    for env_file in ['.env.secrets', '.env.mcp']:
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '=' in line:
                        key, value = line.strip().split('=', 1)
                        if key.startswith('export '):
                            key = key[7:]
                        os.environ[key] = value.strip('"').strip("'")

class LiveHAIntegrationTester:
    """Live Home Assistant integration tester"""
    
    def __init__(self):
        self.ha_token = os.getenv('HA_TOKEN')
        self.supervisor_token = os.getenv('SUPERVISOR_TOKEN')
        
        # Try different HA endpoints (based on discovery results)
        self.ha_endpoints = [
            'http://supervisor/core/api',
            'http://homeassistant:8123/api',
            'http://localhost:8123/api',
            'http://127.0.0.1:8123/api'
        ]
        
        self.working_endpoint = None
        self.test_results = {}
        
        # Live entities from requirements
        self.entities = {
            'camera': 'camera.rowan_room_fluent',
            'todo': 'todo.rowan_room_cleaning_to_do',
            'notify': 'notify.mobile_app_drews_iphone'
        }
    
    def _find_working_endpoint(self):
        """Find working HA endpoint"""
        print("🔍 Finding working HA endpoint...")

        for endpoint in self.ha_endpoints:
            print(f"   Testing: {endpoint}")

            # First try with HA token
            if self.ha_token:
                try:
                    response = requests.get(
                        f"{endpoint}/",
                        headers={'Authorization': f'Bearer {self.ha_token}'},
                        timeout=10
                    )

                    print(f"   HA Token: {response.status_code}")
                    if response.ok:
                        self.working_endpoint = endpoint
                        print(f"✅ Found working endpoint: {endpoint}")
                        return True

                except Exception as e:
                    print(f"   HA Token: Error - {str(e)[:50]}...")

            # Then try with supervisor token
            if self.supervisor_token:
                try:
                    response = requests.get(
                        f"{endpoint}/",
                        headers={'Authorization': f'Bearer {self.supervisor_token}'},
                        timeout=10
                    )

                    print(f"   Supervisor Token: {response.status_code}")
                    if response.ok:
                        self.working_endpoint = endpoint
                        self.ha_token = self.supervisor_token  # Use supervisor token
                        print(f"✅ Found working endpoint with supervisor token: {endpoint}")
                        return True

                except Exception as e:
                    print(f"   Supervisor Token: Error - {str(e)[:50]}...")

        print("❌ No working HA endpoint found")
        return False
    
    def test_camera_entity(self):
        """Test camera entity access"""
        print(f"\\n📷 Testing Camera Entity: {self.entities['camera']}")
        
        try:
            # Test camera state
            state_url = f"{self.working_endpoint}/states/{self.entities['camera']}"
            response = requests.get(
                state_url,
                headers={'Authorization': f'Bearer {self.ha_token}'},
                timeout=10
            )
            
            if response.ok:
                state_data = response.json()
                print(f"   ✅ Camera state: {state_data.get('state', 'unknown')}")
                print(f"   Last updated: {state_data.get('last_updated', 'unknown')}")
                
                # Test camera snapshot
                snapshot_url = f"{self.working_endpoint}/camera_proxy/{self.entities['camera']}"
                snapshot_response = requests.get(
                    snapshot_url,
                    headers={'Authorization': f'Bearer {self.ha_token}'},
                    timeout=15
                )
                
                if snapshot_response.ok:
                    print(f"   ✅ Camera snapshot: {len(snapshot_response.content):,} bytes")
                    
                    # Save snapshot
                    with open('/tmp/live_integration_test_snapshot.jpg', 'wb') as f:
                        f.write(snapshot_response.content)
                    
                    self.test_results['camera'] = {
                        'status': 'success',
                        'state': state_data.get('state'),
                        'snapshot_size': len(snapshot_response.content),
                        'last_updated': state_data.get('last_updated')
                    }
                    return True
                else:
                    print(f"   ❌ Camera snapshot failed: {snapshot_response.status_code}")
            else:
                print(f"   ❌ Camera state failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Camera test failed: {e}")
        
        self.test_results['camera'] = {'status': 'failed', 'error': str(e) if 'e' in locals() else 'Unknown error'}
        return False
    
    def test_todo_entity(self):
        """Test todo list entity"""
        print(f"\\n📝 Testing Todo Entity: {self.entities['todo']}")
        
        try:
            # Test todo state
            state_url = f"{self.working_endpoint}/states/{self.entities['todo']}"
            response = requests.get(
                state_url,
                headers={'Authorization': f'Bearer {self.ha_token}'},
                timeout=10
            )
            
            if response.ok:
                state_data = response.json()
                print(f"   ✅ Todo state: {state_data.get('state', 'unknown')}")
                
                attributes = state_data.get('attributes', {})
                print(f"   Items: {len(attributes.get('items', []))}")
                
                # Test adding a todo item
                test_item = f"AICleaner Integration Test - {datetime.now().strftime('%H:%M:%S')}"
                add_url = f"{self.working_endpoint}/services/todo/add_item"
                
                add_response = requests.post(
                    add_url,
                    headers={'Authorization': f'Bearer {self.ha_token}'},
                    json={
                        'entity_id': self.entities['todo'],
                        'item': test_item
                    },
                    timeout=10
                )
                
                if add_response.ok:
                    print(f"   ✅ Added test item: {test_item}")
                    
                    # Wait a moment and check if item was added
                    time.sleep(2)
                    
                    # Get updated state
                    updated_response = requests.get(
                        state_url,
                        headers={'Authorization': f'Bearer {self.ha_token}'},
                        timeout=10
                    )
                    
                    if updated_response.ok:
                        updated_data = updated_response.json()
                        updated_items = updated_data.get('attributes', {}).get('items', [])
                        
                        # Check if our test item is there
                        test_item_found = any(item.get('summary') == test_item for item in updated_items)
                        
                        if test_item_found:
                            print(f"   ✅ Test item confirmed in todo list")
                        else:
                            print(f"   ⚠️  Test item not found in updated list")
                    
                    self.test_results['todo'] = {
                        'status': 'success',
                        'state': state_data.get('state'),
                        'items_count': len(attributes.get('items', [])),
                        'test_item_added': test_item
                    }
                    return True
                else:
                    print(f"   ❌ Failed to add test item: {add_response.status_code}")
            else:
                print(f"   ❌ Todo state failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Todo test failed: {e}")
        
        self.test_results['todo'] = {'status': 'failed', 'error': str(e) if 'e' in locals() else 'Unknown error'}
        return False
    
    def test_notification_entity(self):
        """Test notification entity"""
        print(f"\\n📱 Testing Notification Entity: {self.entities['notify']}")
        
        try:
            # Test sending a notification
            test_message = f"AICleaner Integration Test - {datetime.now().strftime('%H:%M:%S')}"
            notify_url = f"{self.working_endpoint}/services/notify/mobile_app_drews_iphone"
            
            notify_response = requests.post(
                notify_url,
                headers={'Authorization': f'Bearer {self.ha_token}'},
                json={
                    'message': test_message,
                    'title': 'AICleaner Integration Test'
                },
                timeout=10
            )
            
            if notify_response.ok:
                print(f"   ✅ Test notification sent: {test_message}")
                
                self.test_results['notify'] = {
                    'status': 'success',
                    'test_message': test_message
                }
                return True
            else:
                print(f"   ❌ Notification failed: {notify_response.status_code}")
                print(f"   Response: {notify_response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Notification test failed: {e}")
        
        self.test_results['notify'] = {'status': 'failed', 'error': str(e) if 'e' in locals() else 'Unknown error'}
        return False
    
    def run_integration_tests(self):
        """Run all integration tests"""
        print("🏠 Live Home Assistant Integration Testing")
        print("=" * 60)
        print(f"🔑 HA Token: {self.ha_token[:20]}..." if self.ha_token else "❌ No HA Token")
        
        # Find working endpoint
        if not self._find_working_endpoint():
            return False
        
        # Run entity tests
        camera_ok = self.test_camera_entity()
        todo_ok = self.test_todo_entity()
        notify_ok = self.test_notification_entity()
        
        # Summary
        print("\\n📊 Integration Test Results")
        print("=" * 60)
        
        total_tests = 3
        passed_tests = sum([camera_ok, todo_ok, notify_ok])
        
        print(f"✅ Passed: {passed_tests}/{total_tests}")
        print(f"❌ Failed: {total_tests - passed_tests}/{total_tests}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Detailed results
        for entity_type, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'success' else "❌"
            print(f"   {status_icon} {entity_type}: {result['status']}")
        
        # Save results
        results = {
            'timestamp': datetime.now().isoformat(),
            'endpoint': self.working_endpoint,
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': (passed_tests/total_tests)*100,
            'entity_results': self.test_results
        }
        
        with open('/tmp/live_ha_integration_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\\n💾 Results saved to: /tmp/live_ha_integration_results.json")
        
        return passed_tests == total_tests

def main():
    """Main integration testing function"""
    load_env()
    
    tester = LiveHAIntegrationTester()
    success = tester.run_integration_tests()
    
    if success:
        print("\\n🎉 All integration tests passed!")
    else:
        print("\\n⚠️  Some integration tests failed. Check results for details.")
    
    return success

if __name__ == "__main__":
    main()
