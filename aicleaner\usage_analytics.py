"""
Usage Analytics System for AICleaner
Tracks feature adoption, user behavior patterns, and system utilization metrics
"""

import os
import json
import time
import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from enum import Enum


class EventType(Enum):
    """Types of analytics events"""
    FEATURE_USAGE = "feature_usage"
    USER_ACTION = "user_action"
    SYSTEM_EVENT = "system_event"
    PERFORMANCE = "performance"
    ERROR = "error"


class FeatureCategory(Enum):
    """Feature categories for analytics"""
    AI_ANALYSIS = "ai_analysis"
    MOBILE = "mobile"
    GAMIFICATION = "gamification"
    NOTIFICATIONS = "notifications"
    CONFIGURATION = "configuration"
    AUTOMATION = "automation"
    DASHBOARD = "dashboard"


@dataclass
class AnalyticsEvent:
    """Individual analytics event"""
    timestamp: str
    event_type: EventType
    feature: str
    category: FeatureCategory
    action: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    properties: Dict[str, Any] = None
    duration_ms: Optional[int] = None
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}


@dataclass
class FeatureUsageStats:
    """Feature usage statistics"""
    feature_name: str
    category: str
    total_uses: int
    unique_sessions: int
    avg_duration_ms: float
    first_used: str
    last_used: str
    usage_trend: str  # "increasing", "decreasing", "stable"
    adoption_rate: float  # percentage of sessions that used this feature


@dataclass
class UserBehaviorPattern:
    """User behavior pattern analysis"""
    pattern_name: str
    frequency: int
    confidence: float
    description: str
    features_involved: List[str]
    typical_sequence: List[str]
    avg_session_duration: float


class UsageAnalytics:
    """
    Comprehensive usage analytics system for AICleaner
    
    Features:
    - Feature adoption tracking
    - User behavior pattern analysis
    - System utilization metrics
    - Performance analytics
    - Privacy-preserving analytics (no PII)
    - Trend analysis and insights
    """
    
    def __init__(self, data_dir: str = "/data"):
        """
        Initialize usage analytics system
        
        Args:
            data_dir: Directory for storing analytics data
        """
        self.data_dir = data_dir
        self.analytics_dir = os.path.join(data_dir, "analytics")
        
        # Ensure analytics directory exists
        os.makedirs(self.analytics_dir, exist_ok=True)
        
        # In-memory event storage for fast access
        self.recent_events = []
        self.session_data = defaultdict(list)
        self.feature_usage = defaultdict(int)
        self.daily_stats = defaultdict(lambda: defaultdict(int))
        
        # Privacy settings
        self.anonymize_users = True
        self.retention_days = 30
        
        # Current session tracking
        self.current_session_id = self._generate_session_id()
        self.session_start_time = time.time()
        
        # Load existing analytics data
        self._load_analytics_data()
        
        # Feature registry
        self.registered_features = self._setup_feature_registry()
    
    def _generate_session_id(self) -> str:
        """Generate a unique session ID"""
        timestamp = str(int(time.time() * 1000))
        return hashlib.md5(timestamp.encode()).hexdigest()[:12]
    
    def _generate_user_id(self, identifier: str) -> str:
        """Generate anonymized user ID from identifier"""
        if self.anonymize_users:
            return hashlib.sha256(identifier.encode()).hexdigest()[:16]
        return identifier
    
    def track_feature_usage(self, feature: str, category: FeatureCategory, 
                           action: str = "used", user_identifier: Optional[str] = None,
                           properties: Optional[Dict[str, Any]] = None,
                           duration_ms: Optional[int] = None):
        """
        Track feature usage event
        
        Args:
            feature: Feature name
            category: Feature category
            action: Action performed
            user_identifier: User identifier (will be anonymized)
            properties: Additional properties
            duration_ms: Duration of the action in milliseconds
        """
        user_id = None
        if user_identifier:
            user_id = self._generate_user_id(user_identifier)
        
        event = AnalyticsEvent(
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type=EventType.FEATURE_USAGE,
            feature=feature,
            category=category,
            action=action,
            user_id=user_id,
            session_id=self.current_session_id,
            properties=properties or {},
            duration_ms=duration_ms
        )
        
        self._record_event(event)
    
    def track_user_action(self, action: str, feature: str, category: FeatureCategory,
                         user_identifier: Optional[str] = None,
                         properties: Optional[Dict[str, Any]] = None):
        """
        Track user action event
        
        Args:
            action: Action performed
            feature: Feature involved
            category: Feature category
            user_identifier: User identifier (will be anonymized)
            properties: Additional properties
        """
        user_id = None
        if user_identifier:
            user_id = self._generate_user_id(user_identifier)
        
        event = AnalyticsEvent(
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type=EventType.USER_ACTION,
            feature=feature,
            category=category,
            action=action,
            user_id=user_id,
            session_id=self.current_session_id,
            properties=properties or {}
        )
        
        self._record_event(event)
    
    def track_system_event(self, event_name: str, category: FeatureCategory,
                          properties: Optional[Dict[str, Any]] = None):
        """
        Track system event
        
        Args:
            event_name: Name of the system event
            category: Event category
            properties: Additional properties
        """
        event = AnalyticsEvent(
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type=EventType.SYSTEM_EVENT,
            feature=event_name,
            category=category,
            action="occurred",
            session_id=self.current_session_id,
            properties=properties or {}
        )
        
        self._record_event(event)
    
    def _record_event(self, event: AnalyticsEvent):
        """Record an analytics event"""
        # Add to recent events
        self.recent_events.append(event)
        
        # Keep only recent events in memory (last 1000)
        if len(self.recent_events) > 1000:
            self.recent_events = self.recent_events[-1000:]
        
        # Update session data
        self.session_data[event.session_id].append(event)
        
        # Update feature usage counters
        feature_key = f"{event.category.value}_{event.feature}"
        self.feature_usage[feature_key] += 1
        
        # Update daily stats
        date_key = datetime.fromisoformat(event.timestamp.replace('Z', '+00:00')).strftime('%Y-%m-%d')
        self.daily_stats[date_key][feature_key] += 1
        self.daily_stats[date_key]["total_events"] += 1
        
        # Persist event
        self._persist_event(event)
    
    def get_feature_usage_stats(self, days: int = 7) -> List[FeatureUsageStats]:
        """
        Get feature usage statistics
        
        Args:
            days: Number of days to analyze
            
        Returns:
            List of feature usage statistics
        """
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=days)
        
        # Filter events within time range
        recent_events = [
            event for event in self.recent_events
            if datetime.fromisoformat(event.timestamp.replace('Z', '+00:00')) >= cutoff_time
        ]
        
        # Group by feature
        feature_data = defaultdict(list)
        for event in recent_events:
            if event.event_type == EventType.FEATURE_USAGE:
                feature_key = f"{event.category.value}_{event.feature}"
                feature_data[feature_key].append(event)
        
        # Calculate statistics for each feature
        stats = []
        total_sessions = len(set(event.session_id for event in recent_events))
        
        for feature_key, events in feature_data.items():
            if not events:
                continue
            
            category, feature_name = feature_key.split('_', 1)
            
            # Calculate metrics
            total_uses = len(events)
            unique_sessions = len(set(event.session_id for event in events))
            
            durations = [event.duration_ms for event in events if event.duration_ms is not None]
            avg_duration_ms = sum(durations) / len(durations) if durations else 0
            
            timestamps = [event.timestamp for event in events]
            first_used = min(timestamps)
            last_used = max(timestamps)
            
            # Calculate adoption rate
            adoption_rate = (unique_sessions / total_sessions * 100) if total_sessions > 0 else 0
            
            # Calculate usage trend
            usage_trend = self._calculate_usage_trend(feature_key, days)
            
            stats.append(FeatureUsageStats(
                feature_name=feature_name,
                category=category,
                total_uses=total_uses,
                unique_sessions=unique_sessions,
                avg_duration_ms=avg_duration_ms,
                first_used=first_used,
                last_used=last_used,
                usage_trend=usage_trend,
                adoption_rate=adoption_rate
            ))
        
        # Sort by total uses
        stats.sort(key=lambda x: x.total_uses, reverse=True)
        return stats
    
    def analyze_user_behavior_patterns(self, days: int = 7) -> List[UserBehaviorPattern]:
        """
        Analyze user behavior patterns
        
        Args:
            days: Number of days to analyze
            
        Returns:
            List of identified behavior patterns
        """
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=days)
        
        # Filter events within time range
        recent_events = [
            event for event in self.recent_events
            if datetime.fromisoformat(event.timestamp.replace('Z', '+00:00')) >= cutoff_time
        ]
        
        # Group events by session
        session_sequences = defaultdict(list)
        for event in recent_events:
            session_sequences[event.session_id].append(event)
        
        # Sort events within each session by timestamp
        for session_id in session_sequences:
            session_sequences[session_id].sort(key=lambda x: x.timestamp)
        
        # Identify common patterns
        patterns = []
        
        # Pattern 1: Feature co-usage
        co_usage_patterns = self._find_co_usage_patterns(session_sequences)
        patterns.extend(co_usage_patterns)
        
        # Pattern 2: Sequential usage patterns
        sequential_patterns = self._find_sequential_patterns(session_sequences)
        patterns.extend(sequential_patterns)
        
        # Pattern 3: Time-based patterns
        time_patterns = self._find_time_based_patterns(recent_events)
        patterns.extend(time_patterns)
        
        return patterns
    
    def get_system_utilization_metrics(self, days: int = 7) -> Dict[str, Any]:
        """
        Get system utilization metrics
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with utilization metrics
        """
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=days)
        
        # Filter events within time range
        recent_events = [
            event for event in self.recent_events
            if datetime.fromisoformat(event.timestamp.replace('Z', '+00:00')) >= cutoff_time
        ]
        
        # Calculate metrics
        total_events = len(recent_events)
        unique_sessions = len(set(event.session_id for event in recent_events))
        unique_features = len(set(f"{event.category.value}_{event.feature}" for event in recent_events))
        
        # Events per day
        daily_events = defaultdict(int)
        for event in recent_events:
            date_key = datetime.fromisoformat(event.timestamp.replace('Z', '+00:00')).strftime('%Y-%m-%d')
            daily_events[date_key] += 1
        
        avg_events_per_day = sum(daily_events.values()) / len(daily_events) if daily_events else 0
        
        # Category distribution
        category_distribution = Counter(event.category.value for event in recent_events)
        
        # Session duration analysis
        session_durations = []
        for session_id, events in self.session_data.items():
            if len(events) >= 2:
                start_time = min(datetime.fromisoformat(e.timestamp.replace('Z', '+00:00')) for e in events)
                end_time = max(datetime.fromisoformat(e.timestamp.replace('Z', '+00:00')) for e in events)
                duration_minutes = (end_time - start_time).total_seconds() / 60
                session_durations.append(duration_minutes)
        
        avg_session_duration = sum(session_durations) / len(session_durations) if session_durations else 0
        
        return {
            "period_days": days,
            "total_events": total_events,
            "unique_sessions": unique_sessions,
            "unique_features_used": unique_features,
            "avg_events_per_day": avg_events_per_day,
            "avg_session_duration_minutes": avg_session_duration,
            "category_distribution": dict(category_distribution),
            "daily_events": dict(daily_events),
            "most_active_day": max(daily_events.items(), key=lambda x: x[1])[0] if daily_events else None
        }
    
    def _calculate_usage_trend(self, feature_key: str, days: int) -> str:
        """Calculate usage trend for a feature"""
        # Get daily usage for the feature
        daily_usage = []
        for i in range(days):
            date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
            usage = self.daily_stats.get(date, {}).get(feature_key, 0)
            daily_usage.append(usage)
        
        if len(daily_usage) < 3:
            return "stable"
        
        # Calculate trend using simple linear regression
        recent_avg = sum(daily_usage[:days//2]) / (days//2) if days//2 > 0 else 0
        older_avg = sum(daily_usage[days//2:]) / (days - days//2) if days - days//2 > 0 else 0
        
        if recent_avg > older_avg * 1.2:
            return "increasing"
        elif recent_avg < older_avg * 0.8:
            return "decreasing"
        else:
            return "stable"
    
    def _find_co_usage_patterns(self, session_sequences: Dict[str, List[AnalyticsEvent]]) -> List[UserBehaviorPattern]:
        """Find features that are commonly used together"""
        patterns = []
        
        # Count feature co-occurrences within sessions
        co_occurrences = defaultdict(int)
        
        for session_events in session_sequences.values():
            features_in_session = set(f"{event.category.value}_{event.feature}" for event in session_events)
            
            # Count all pairs of features in this session
            features_list = list(features_in_session)
            for i in range(len(features_list)):
                for j in range(i + 1, len(features_list)):
                    pair = tuple(sorted([features_list[i], features_list[j]]))
                    co_occurrences[pair] += 1
        
        # Identify significant co-usage patterns
        total_sessions = len(session_sequences)
        for (feature1, feature2), count in co_occurrences.items():
            if count >= 3 and count / total_sessions >= 0.1:  # At least 3 occurrences and 10% of sessions
                confidence = count / total_sessions
                patterns.append(UserBehaviorPattern(
                    pattern_name=f"Co-usage: {feature1} + {feature2}",
                    frequency=count,
                    confidence=confidence,
                    description=f"Users often use {feature1} and {feature2} together",
                    features_involved=[feature1, feature2],
                    typical_sequence=[feature1, feature2],
                    avg_session_duration=0.0  # Would need to calculate
                ))
        
        return patterns
    
    def _find_sequential_patterns(self, session_sequences: Dict[str, List[AnalyticsEvent]]) -> List[UserBehaviorPattern]:
        """Find common sequential usage patterns"""
        patterns = []
        
        # Extract sequences of feature usage
        sequences = []
        for session_events in session_sequences.values():
            if len(session_events) >= 2:
                sequence = [f"{event.category.value}_{event.feature}" for event in session_events]
                sequences.append(sequence)
        
        # Find common subsequences of length 2-3
        for seq_length in [2, 3]:
            subsequence_counts = defaultdict(int)
            
            for sequence in sequences:
                for i in range(len(sequence) - seq_length + 1):
                    subseq = tuple(sequence[i:i + seq_length])
                    subsequence_counts[subseq] += 1
            
            # Identify significant patterns
            for subseq, count in subsequence_counts.items():
                if count >= 3 and count / len(sequences) >= 0.1:
                    confidence = count / len(sequences)
                    patterns.append(UserBehaviorPattern(
                        pattern_name=f"Sequential: {' → '.join(subseq)}",
                        frequency=count,
                        confidence=confidence,
                        description=f"Users commonly follow this sequence: {' → '.join(subseq)}",
                        features_involved=list(subseq),
                        typical_sequence=list(subseq),
                        avg_session_duration=0.0
                    ))
        
        return patterns
    
    def _find_time_based_patterns(self, events: List[AnalyticsEvent]) -> List[UserBehaviorPattern]:
        """Find time-based usage patterns"""
        patterns = []
        
        # Group events by hour of day
        hourly_usage = defaultdict(int)
        for event in events:
            hour = datetime.fromisoformat(event.timestamp.replace('Z', '+00:00')).hour
            hourly_usage[hour] += 1
        
        # Find peak usage hours
        if hourly_usage:
            peak_hour = max(hourly_usage.items(), key=lambda x: x[1])
            if peak_hour[1] >= 10:  # At least 10 events in peak hour
                patterns.append(UserBehaviorPattern(
                    pattern_name=f"Peak Usage Hour: {peak_hour[0]}:00",
                    frequency=peak_hour[1],
                    confidence=peak_hour[1] / sum(hourly_usage.values()),
                    description=f"Most activity occurs around {peak_hour[0]}:00",
                    features_involved=["time_based"],
                    typical_sequence=[f"hour_{peak_hour[0]}"],
                    avg_session_duration=0.0
                ))
        
        return patterns
    
    def _setup_feature_registry(self) -> Dict[str, Dict[str, Any]]:
        """Setup registry of trackable features"""
        return {
            "ai_analysis": {
                "category": FeatureCategory.AI_ANALYSIS,
                "actions": ["analyze", "cache_hit", "cache_miss", "batch_analysis"]
            },
            "mobile_pwa": {
                "category": FeatureCategory.MOBILE,
                "actions": ["install", "open", "offline_use", "push_notification"]
            },
            "gamification_achievement": {
                "category": FeatureCategory.GAMIFICATION,
                "actions": ["unlock", "view", "share"]
            },
            "notification_smart": {
                "category": FeatureCategory.NOTIFICATIONS,
                "actions": ["send", "open", "dismiss", "configure"]
            },
            "dashboard_card": {
                "category": FeatureCategory.DASHBOARD,
                "actions": ["view", "interact", "refresh"]
            }
        }
    
    def _persist_event(self, event: AnalyticsEvent):
        """Persist analytics event to disk"""
        try:
            # Store events in daily files
            date_str = datetime.fromisoformat(event.timestamp.replace('Z', '+00:00')).strftime('%Y-%m-%d')
            events_file = os.path.join(self.analytics_dir, f"events_{date_str}.json")
            
            # Append to daily file
            events = []
            if os.path.exists(events_file):
                with open(events_file, 'r') as f:
                    events = json.load(f)
            
            events.append(asdict(event))
            
            with open(events_file, 'w') as f:
                json.dump(events, f, indent=2, default=str)
                
        except Exception as e:
            print(f"Failed to persist analytics event: {e}")
    
    def _load_analytics_data(self):
        """Load recent analytics data from disk"""
        try:
            # Load last 7 days of events
            for i in range(7):
                date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
                events_file = os.path.join(self.analytics_dir, f"events_{date}.json")
                
                if os.path.exists(events_file):
                    with open(events_file, 'r') as f:
                        events_data = json.load(f)
                    
                    for event_data in events_data:
                        # Convert back to AnalyticsEvent
                        event_data["event_type"] = EventType(event_data["event_type"])
                        event_data["category"] = FeatureCategory(event_data["category"])
                        event = AnalyticsEvent(**event_data)
                        self.recent_events.append(event)
                        
                        # Update session data
                        self.session_data[event.session_id].append(event)
                        
        except Exception as e:
            print(f"Failed to load analytics data: {e}")
    
    def generate_analytics_report(self, days: int = 7) -> Dict[str, Any]:
        """Generate comprehensive analytics report"""
        feature_stats = self.get_feature_usage_stats(days)
        behavior_patterns = self.analyze_user_behavior_patterns(days)
        utilization_metrics = self.get_system_utilization_metrics(days)
        
        return {
            "report_generated": datetime.now(timezone.utc).isoformat(),
            "period_days": days,
            "feature_usage": [asdict(stat) for stat in feature_stats],
            "behavior_patterns": [asdict(pattern) for pattern in behavior_patterns],
            "system_utilization": utilization_metrics,
            "top_features": [stat.feature_name for stat in feature_stats[:5]],
            "adoption_insights": self._generate_adoption_insights(feature_stats),
            "recommendations": self._generate_recommendations(feature_stats, behavior_patterns)
        }
    
    def _generate_adoption_insights(self, feature_stats: List[FeatureUsageStats]) -> List[str]:
        """Generate insights about feature adoption"""
        insights = []
        
        if feature_stats:
            # High adoption features
            high_adoption = [stat for stat in feature_stats if stat.adoption_rate > 50]
            if high_adoption:
                insights.append(f"High adoption features: {', '.join(stat.feature_name for stat in high_adoption[:3])}")
            
            # Low adoption features
            low_adoption = [stat for stat in feature_stats if stat.adoption_rate < 20]
            if low_adoption:
                insights.append(f"Low adoption features needing attention: {', '.join(stat.feature_name for stat in low_adoption[:3])}")
            
            # Trending features
            trending_up = [stat for stat in feature_stats if stat.usage_trend == "increasing"]
            if trending_up:
                insights.append(f"Trending up: {', '.join(stat.feature_name for stat in trending_up[:3])}")
        
        return insights
    
    def _generate_recommendations(self, feature_stats: List[FeatureUsageStats], 
                                 patterns: List[UserBehaviorPattern]) -> List[str]:
        """Generate recommendations based on analytics"""
        recommendations = []
        
        # Feature improvement recommendations
        if feature_stats:
            low_adoption = [stat for stat in feature_stats if stat.adoption_rate < 20]
            if low_adoption:
                recommendations.append(f"Consider improving onboarding for: {low_adoption[0].feature_name}")
        
        # Pattern-based recommendations
        if patterns:
            co_usage_patterns = [p for p in patterns if "Co-usage" in p.pattern_name]
            if co_usage_patterns:
                recommendations.append("Consider creating feature bundles based on co-usage patterns")
        
        return recommendations
