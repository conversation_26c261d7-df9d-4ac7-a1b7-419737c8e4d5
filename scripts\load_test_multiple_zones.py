#!/usr/bin/env python3
"""
Load testing with multiple zones for AICleaner performance verification
"""
import os
import sys
import time
import asyncio
import concurrent.futures
import threading
from datetime import datetime
import json

# Add the aicleaner module to path
sys.path.append('/root/addons/Aiclean')
sys.path.append('/root/addons/Aiclean/aicleaner')

def load_env():
    """Load environment variables"""
    for env_file in ['.env.secrets', '.env.mcp']:
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '=' in line:
                        key, value = line.strip().split('=', 1)
                        if key.startswith('export '):
                            key = key[7:]
                        os.environ[key] = value.strip('"').strip("'")

def create_test_zone_config(zone_name, zone_id):
    """Create a test zone configuration"""
    return {
        'name': zone_name,
        'icon': f'mdi:home-{zone_id}',
        'purpose': f'Keep {zone_name} clean and organized',
        'camera_entity': 'camera.rowan_room_fluent',  # Use same camera for all zones
        'todo_list_entity': f'todo.{zone_name.lower()}_tasks',
        'update_frequency': 24,
        'notifications_enabled': False,  # Disable for load testing
        'notification_service': 'notify.test',
        'notification_personality': 'default',
        'notify_on_create': False,
        'notify_on_complete': False
    }

def simulate_zone_analysis(zone_config, zone_id, results_dict):
    """Simulate a zone analysis cycle"""
    zone_name = zone_config['name']
    start_time = time.time()
    
    try:
        print(f"🔄 Zone {zone_id} ({zone_name}): Starting analysis...")
        
        # Simulate camera snapshot (0.2s based on real test)
        time.sleep(0.2)
        snapshot_time = time.time() - start_time
        
        # Simulate AI analysis (3 calls, ~2s each based on real test)
        ai_start = time.time()
        
        # Completed tasks analysis
        time.sleep(1.9)
        completed_time = time.time() - ai_start
        
        # New tasks analysis  
        time.sleep(2.2)
        new_tasks_time = time.time() - ai_start - completed_time
        
        # Cleanliness analysis
        time.sleep(1.8)
        cleanliness_time = time.time() - ai_start - completed_time - new_tasks_time
        
        total_ai_time = completed_time + new_tasks_time + cleanliness_time
        
        # Simulate HA API calls (todo updates, sensor updates)
        ha_start = time.time()
        time.sleep(0.3)  # Simulate multiple HA API calls
        ha_time = time.time() - ha_start
        
        total_time = time.time() - start_time
        
        # Store results
        results_dict[zone_id] = {
            'zone_name': zone_name,
            'total_time': total_time,
            'snapshot_time': snapshot_time,
            'ai_analysis_time': total_ai_time,
            'ha_api_time': ha_time,
            'success': True,
            'timestamp': datetime.now().isoformat()
        }
        
        print(f"✅ Zone {zone_id} ({zone_name}): Completed in {total_time:.2f}s")
        
    except Exception as e:
        results_dict[zone_id] = {
            'zone_name': zone_name,
            'error': str(e),
            'success': False,
            'timestamp': datetime.now().isoformat()
        }
        print(f"❌ Zone {zone_id} ({zone_name}): Failed - {e}")

def run_concurrent_load_test(num_zones=5):
    """Run concurrent load test with multiple zones"""
    print(f"🚀 Starting Load Test with {num_zones} Concurrent Zones")
    print("=" * 60)
    
    # Create zone configurations
    zone_configs = []
    for i in range(num_zones):
        zone_name = f"TestZone{i+1}"
        zone_configs.append(create_test_zone_config(zone_name, i+1))
    
    # Results storage
    results = {}
    
    # Start concurrent analysis
    start_time = time.time()
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_zones) as executor:
        futures = []
        for i, config in enumerate(zone_configs):
            future = executor.submit(simulate_zone_analysis, config, i+1, results)
            futures.append(future)
        
        # Wait for all to complete
        concurrent.futures.wait(futures)
    
    total_test_time = time.time() - start_time
    
    # Analyze results
    print("\n📊 Load Test Results")
    print("=" * 60)
    
    successful_zones = [r for r in results.values() if r.get('success', False)]
    failed_zones = [r for r in results.values() if not r.get('success', False)]
    
    print(f"✅ Successful zones: {len(successful_zones)}/{num_zones}")
    print(f"❌ Failed zones: {len(failed_zones)}")
    print(f"⏱️  Total test time: {total_test_time:.2f} seconds")
    
    if successful_zones:
        avg_time = sum(r['total_time'] for r in successful_zones) / len(successful_zones)
        max_time = max(r['total_time'] for r in successful_zones)
        min_time = min(r['total_time'] for r in successful_zones)
        
        print(f"📈 Performance Metrics:")
        print(f"   Average zone time: {avg_time:.2f}s")
        print(f"   Fastest zone: {min_time:.2f}s")
        print(f"   Slowest zone: {max_time:.2f}s")
        print(f"   Efficiency: {(avg_time / total_test_time) * 100:.1f}% (concurrent benefit)")
        
        # Detailed breakdown
        avg_snapshot = sum(r['snapshot_time'] for r in successful_zones) / len(successful_zones)
        avg_ai = sum(r['ai_analysis_time'] for r in successful_zones) / len(successful_zones)
        avg_ha = sum(r['ha_api_time'] for r in successful_zones) / len(successful_zones)
        
        print(f"📋 Average Component Times:")
        print(f"   Camera snapshot: {avg_snapshot:.2f}s")
        print(f"   AI analysis: {avg_ai:.2f}s")
        print(f"   HA API calls: {avg_ha:.2f}s")
    
    # Save detailed results
    with open('/tmp/load_test_results.json', 'w') as f:
        json.dump({
            'test_config': {
                'num_zones': num_zones,
                'total_test_time': total_test_time,
                'timestamp': datetime.now().isoformat()
            },
            'results': results
        }, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: /tmp/load_test_results.json")
    
    return len(successful_zones) == num_zones

def main():
    """Main load testing function"""
    load_env()
    
    print("🧪 AICleaner Multi-Zone Load Testing")
    print("=" * 60)
    
    # Test with different zone counts
    test_scenarios = [3, 5, 8]
    
    for num_zones in test_scenarios:
        print(f"\n🎯 Testing with {num_zones} zones...")
        success = run_concurrent_load_test(num_zones)
        
        if success:
            print(f"✅ {num_zones}-zone test: PASSED")
        else:
            print(f"❌ {num_zones}-zone test: FAILED")
        
        # Brief pause between tests
        time.sleep(2)
    
    print("\n🏁 Load Testing Complete!")

if __name__ == "__main__":
    main()
