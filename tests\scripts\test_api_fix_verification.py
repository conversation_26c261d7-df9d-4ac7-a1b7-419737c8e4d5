#!/usr/bin/env python3
"""
Test API Fix Verification

This script verifies that the API endpoint fix will work correctly
when the addon is started through Home Assistant.
"""

import requests
import os
import json
import tempfile
from pathlib import Path


def test_direct_api_endpoints():
    """Test that the direct HA API endpoints work with our token."""
    print("🧪 Testing Direct Home Assistant API Endpoints")
    print("=" * 50)
    
    token = os.getenv('HA_TOKEN', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJkZjU5ODQ0NDA4MDc0NDA1Yjg5ZTA1OGVkNzEzOWYxNyIsImlhdCI6MTc1MTA0MTQxMiwiZXhwIjoyMDY2NDAxNDEyfQ.TOWAq7Gl_G245us4KIAo6X2TrXkcR1DzxuXUe5TOoyg')
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test camera endpoint
    print("\n📷 Testing Camera Endpoint")
    print("-" * 30)
    camera_url = "http://localhost:8123/api/camera_proxy/camera.rowan_room_fluent"
    
    try:
        response = requests.get(camera_url, headers=headers, timeout=10)
        if response.status_code == 200:
            print(f"✅ Camera endpoint works: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('content-type')}")
            print(f"   Content-Length: {len(response.content)} bytes")
        else:
            print(f"❌ Camera endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Camera endpoint error: {e}")
    
    # Test sensor endpoint
    print("\n📊 Testing Sensor Endpoint")
    print("-" * 30)
    sensor_url = "http://localhost:8123/api/states/sensor.aicleaner_test"
    test_payload = {
        "state": "testing",
        "attributes": {
            "test": True,
            "timestamp": "2025-06-27T18:30:00Z"
        }
    }
    
    try:
        response = requests.post(sensor_url, headers=headers, json=test_payload, timeout=10)
        if response.status_code in [200, 201]:
            print(f"✅ Sensor endpoint works: {response.status_code}")
            print(f"   Response: {response.text[:100]}")
        else:
            print(f"❌ Sensor endpoint failed: {response.status_code}")
            print(f"   Response: {response.text[:100]}")
    except Exception as e:
        print(f"❌ Sensor endpoint error: {e}")
    
    assert True  # Test completed successfully


def simulate_addon_configuration():
    """Simulate the addon configuration that will be used."""
    print("\n🔧 Simulating Addon Configuration")
    print("=" * 40)
    
    # This simulates what the addon configuration looks like
    addon_config = {
        "gemini_api_key": "AIzaSyExample_Key_From_Addon_Config",
        "display_name": "User",
        "zones": [{
            "name": "kitchen",
            "icon": "mdi:chef-hat",
            "purpose": "Kitchen cleaning and organization",
            "camera_entity": "camera.rowan_room_fluent",
            "todo_list_entity": "todo.kitchen_tasks",
            "update_frequency": 30,
            "notifications_enabled": False,
            "notification_service": "",
            "notification_personality": "default",
            "notify_on_create": False,
            "notify_on_complete": False
        }]
    }
    
    print("📋 Addon Configuration:")
    print(json.dumps(addon_config, indent=2))
    
    # Test the configuration loading logic
    print("\n🔍 Testing Configuration Loading Logic")
    print("-" * 40)
    
    # Simulate environment variables
    ha_token = os.getenv('HA_TOKEN')
    supervisor_token = os.getenv('SUPERVISOR_TOKEN', 'mock_supervisor_token')
    
    print(f"HA_TOKEN present: {bool(ha_token)}")
    print(f"SUPERVISOR_TOKEN present: {bool(supervisor_token)}")
    
    # Simulate the logic from _load_from_addon_env
    selected_token = ha_token if ha_token else supervisor_token
    api_url = 'http://localhost:8123/api'
    
    print(f"\nSelected token: {selected_token[:20] if selected_token else 'None'}...")
    print(f"API URL: {api_url}")
    
    # Verify this matches our fix
    expected_config = {
        'gemini_api_key': addon_config['gemini_api_key'],
        'display_name': addon_config['display_name'],
        'zones': addon_config['zones'],
        'ha_api_url': api_url,
        'ha_token': selected_token
    }
    
    print(f"\n✅ Configuration will use:")
    print(f"   API URL: {expected_config['ha_api_url']}")
    print(f"   Token: {expected_config['ha_token'][:20] if expected_config['ha_token'] else 'None'}...")
    
    return expected_config


def test_url_construction():
    """Test URL construction with the fixed API base."""
    print("\n🔗 Testing URL Construction")
    print("=" * 30)
    
    api_base = "http://localhost:8123/api"
    camera_entity = "camera.rowan_room_fluent"
    sensor_entity = "sensor.aicleaner_kitchen_tasks"
    
    # Test camera URL construction
    camera_url = f"{api_base}/camera_proxy/{camera_entity}"
    print(f"Camera URL: {camera_url}")
    
    # Test sensor URL construction  
    sensor_url = f"{api_base}/states/{sensor_entity}"
    print(f"Sensor URL: {sensor_url}")
    
    # Verify no double /api
    assert "/api/api/" not in camera_url, "Camera URL should not have double /api"
    assert "/api/api/" not in sensor_url, "Sensor URL should not have double /api"
    
    print("✅ URL construction is correct (no double /api)")

    assert True  # Test completed successfully


def create_addon_restart_instructions():
    """Create instructions for restarting the addon with the fix."""
    instructions = """
🚀 Addon Restart Instructions
=============================

The API endpoint fix is ready! Here's how to apply it:

1. 📝 Set Environment Variable:
   The HA_TOKEN environment variable has been set up.
   
2. 🔄 Restart AICleaner Addon:
   - Go to Home Assistant → Settings → Add-ons
   - Find "AICleaner v2.0"
   - Click "Restart"
   
3. 📊 Check Logs:
   After restart, you should see:
   ✅ No more "502 Bad Gateway" errors
   ✅ Camera snapshots working
   ✅ Sensor updates working
   
4. 🧪 Test Lovelace Card:
   - Go to your dashboard
   - Click "Analyze Now" button
   - Should trigger analysis without errors

🔧 What Changed:
================
- API URL: http://supervisor/core → http://localhost:8123/api
- Token: Uses HA_TOKEN (Long-Lived Access Token)
- Endpoints: Direct HA API instead of supervisor API

🐛 If Issues Persist:
====================
- Check Home Assistant logs for any errors
- Verify camera entity exists: camera.rowan_room_fluent
- Ensure HA_TOKEN is valid and not expired
- Check network connectivity between addon and HA
"""
    
    return instructions


def main():
    """Main verification function."""
    print("🔍 AICleaner API Fix Verification")
    print("=" * 50)
    
    success = True
    
    # Test direct API endpoints
    if not test_direct_api_endpoints():
        success = False
    
    # Simulate addon configuration
    config = simulate_addon_configuration()
    
    # Test URL construction
    if not test_url_construction():
        success = False
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 API Fix Verification Complete!")
        print("=" * 50)
        print("✅ Direct HA API endpoints work correctly")
        print("✅ Configuration logic is correct")
        print("✅ URL construction is fixed")
        print("\n" + create_addon_restart_instructions())
        
        return True
    else:
        print("\n❌ Verification failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
