#!/usr/bin/env python3
"""
Notion API Helper Script
Provides reliable Notion API integration without process conflicts
"""

import os
import sys
import json
import requests
from typing import Dict, List, Any, Optional

class NotionAPI:
    """Notion API client for workspace updates"""
    
    def __init__(self, token: str = None, page_id: str = None):
        """Initialize Notion API client"""
        self.token = token or os.getenv('NOTION_TOKEN')
        self.page_id = page_id or os.getenv('NOTION_PAGE_ID')
        self.base_url = "https://api.notion.com/v1"
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
            "Notion-Version": "2022-06-28"
        }
        
        if not self.token:
            raise ValueError("NOTION_TOKEN not found in environment")
        if not self.page_id:
            raise ValueError("NOTION_PAGE_ID not found in environment")
    
    def append_blocks(self, blocks: List[Dict[str, Any]], page_id: str = None) -> Dict[str, Any]:
        """Append blocks to a Notion page"""
        target_page = page_id or self.page_id
        url = f"{self.base_url}/blocks/{target_page}/children"
        
        payload = {"children": blocks}
        
        try:
            response = requests.patch(url, headers=self.headers, json=payload, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error updating Notion: {e}")
            if hasattr(e, 'response') and e.response:
                print(f"Response: {e.response.text}")
            return None
    
    def create_callout(self, content: str, emoji: str = "💡", color: str = "default") -> Dict[str, Any]:
        """Create a callout block"""
        return {
            "object": "block",
            "type": "callout",
            "callout": {
                "rich_text": [{"type": "text", "text": {"content": content}}],
                "icon": {"type": "emoji", "emoji": emoji},
                "color": color
            }
        }
    
    def create_heading(self, content: str, level: int = 2) -> Dict[str, Any]:
        """Create a heading block (level 1, 2, or 3)"""
        heading_type = f"heading_{level}"
        return {
            "object": "block",
            "type": heading_type,
            heading_type: {
                "rich_text": [{"type": "text", "text": {"content": content}}]
            }
        }
    
    def create_bullet_point(self, content: str) -> Dict[str, Any]:
        """Create a bullet point block"""
        return {
            "object": "block",
            "type": "bulleted_list_item",
            "bulleted_list_item": {
                "rich_text": [{"type": "text", "text": {"content": content}}]
            }
        }
    
    def create_paragraph(self, content: str) -> Dict[str, Any]:
        """Create a paragraph block"""
        return {
            "object": "block",
            "type": "paragraph",
            "paragraph": {
                "rich_text": [{"type": "text", "text": {"content": content}}]
            }
        }
    
    def create_progress_update(self, title: str, items: List[str], summary: str = None, emoji: str = "🎯") -> List[Dict[str, Any]]:
        """Create a complete progress update with callout, heading, bullets, and summary"""
        blocks = []
        
        # Add callout
        blocks.append(self.create_callout(title, emoji))
        
        # Add heading
        blocks.append(self.create_heading("Progress Update"))
        
        # Add bullet points
        for item in items:
            blocks.append(self.create_bullet_point(item))
        
        # Add summary if provided
        if summary:
            blocks.append(self.create_paragraph(summary))
        
        return blocks

def load_env_vars():
    """Command line interface for Notion API"""
    if len(sys.argv) < 2:
        print("Usage: python notion_api.py <command> [args...]")
        print("Commands:")
        print("  test - Test connection")
        print("  callout <text> [emoji] - Add callout")
        print("  progress <title> <item1,item2,...> [summary] - Add progress update")
        sys.exit(1)
    """Load environment variables from config files"""
    for env_file in ['.env.secrets', '.env.mcp']:
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '=' in line:
                        key, value = line.strip().split('=', 1)
                        if key.startswith('export '):
                            key = key[7:]
                        os.environ[key] = value.strip('"\'')
    
    try:
        # Load environment variables
        load_env_vars()
        
        notion = NotionAPI()
        command = sys.argv[1]
        
        if command == "test":
            print(f"✅ Notion API configured")
            print(f"   Token: {notion.token[:10]}...")
            print(f"   Page ID: {notion.page_id}")
            
        elif command == "callout":
            if len(sys.argv) < 3:
                print("Usage: callout <text> [emoji]")
                sys.exit(1)
            
            text = sys.argv[2]
            emoji = sys.argv[3] if len(sys.argv) > 3 else "💡"
            
            blocks = [notion.create_callout(text, emoji)]
            result = notion.append_blocks(blocks)
            
            if result:
                print(f"✅ Added callout: {text}")
            else:
                print("❌ Failed to add callout")
                sys.exit(1)
        
        elif command == "progress":
            if len(sys.argv) < 4:
                print("Usage: progress <title> <item1,item2,...> [summary]")
                sys.exit(1)
            
            title = sys.argv[2]
            items = sys.argv[3].split(',')
            summary = sys.argv[4] if len(sys.argv) > 4 else None
            
            blocks = notion.create_progress_update(title, items, summary)
            result = notion.append_blocks(blocks)
            
            if result:
                print(f"✅ Added progress update: {title}")
            else:
                print("❌ Failed to add progress update")
                sys.exit(1)
        
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
    
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
